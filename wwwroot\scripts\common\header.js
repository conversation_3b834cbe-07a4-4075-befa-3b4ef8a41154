$(document).ready(function () {
  $body = $("body");
  var $lang = $("html")[0].lang;
  var $offsetWidthBody = $body.width();

  function hoverMenu() {
    $('.site-header .menu-item.level-2.hassub').hover(function () {
      $(this).find('> .wrap-sub-menu').slideDown();
      $(this).find('> .wrap-sub-menu').show();
    }, function () {
        $(this).find('> .wrap-sub-menu').stop(true, false).slideUp();
    });
  }

  function languageDropdown() {
    var $lang = $(".wrap-lang");
    var $langList = $(".lang-list");

    $(".lang-status > a").on("click", function (e) {
      e.preventDefault();
    });

    $(".lang-status").on("click", function (e) {
      e.preventDefault();
      $lang.toggleClass("open-lang");
      if ($langList.css("opacity") == 0) {
        $langList.animate({ opacity: 1 }, 250);
      } else {
        $langList.animate({ opacity: 0 }, 250);
      }
    });

    $(document).on("click", function (event) {
      if (
        !$(event.target).closest(".wrap-lang").length &&
        !$(event.target).is(".lang-list")
      ) {
        $lang.removeClass("open-lang");
        $langList.animate({ opacity: 0 }, 250);
      }
    });
  }


  function menuDropdown() {
    if ($(".header").hasClass("menu-mobile-dropdown")) {
      var Accordion = function (el, multiple) {
  this.el = el || {};
  this.multiple = multiple || false;

  var links = this.el.find(".open-sub");
  links.on(
    "click",
    { el: this.el, multiple: this.multiple },
    this.dropdown
  );
};

Accordion.prototype.dropdown = function (e) {
  var $el = e.data.el;
  var $this = $(this);
  var $next = $this.next("ul.menu-responsive.ul-menu"); // submenu right after .open-sub
  var $parent = $this.parent();

  if (!e.data.multiple) {
    if ($parent.hasClass("open")) {
      // Close current
      $next.stop(true, true).slideUp();
      $parent.removeClass("open");
    } else {
      // Close all other submenus
      $el.find("ul.menu-responsive.ul-menu").stop(true, true).slideUp();
      $el.find(".open").removeClass("open");

      // Open current
      $next.stop(true, true).slideDown();
      $parent.addClass("open");
    }
  } else {
    // Multiple allowed
    $next.stop(true, true).slideToggle();
    $parent.toggleClass("open");
  }
};


      var accordion = new Accordion($(".menu-responsive"), false);

      if ($(window).width() <= 1200) {
        $(".btn-menu-offcanvas").on("click", function () {
          $this = $(this);
          $this.toggleClass("active-menu");
          $("body").toggleClass("open-menu");
          // $this.closest(".header").find(".menu-mobile").slideToggle();
        });
      }
    }
  }

  $(window).scroll(function () {
    if ($(this).scrollTop() > 100) {
      $("#back-to-top").fadeIn();
      $('.site-header').addClass('sticky');
    } else {
      $("#back-to-top").fadeOut();
      $('.site-header').removeClass('sticky');
    }
  });
   // Back to top functionality
   $("#back-to-top").on("click", function () {
    $("html, body").animate(
      {
        scrollTop: 0,
      },
      800
    ); // 800ms is the time to scroll to the top of the page, can be adjusted
    return false;
  });

  function headerSticky() {
    var myElement = document.querySelector(".header");
    var headroom  = new Headroom(myElement);
    headroom.init();
  }

  menuDropdown();

  if ($offsetWidthBody > 1200) {
    hoverMenu();
  }

  $('.search-btn').on('click', function(e) {
    e.preventDefault();
    $('.search-panel').addClass('active');
    $('.search-panel .search-input').focus();
  });

  $('.search-panel .serClose').on('click', function(e) {
    e.preventDefault();
    $('.search-panel').removeClass('active');
  });
  $('.menu-mobile .serClose').on('click', function(e) {
    e.preventDefault();
    $('body').removeClass('open-menu');
  });

  languageDropdown();

  function getSearchBaseUrl() {
    var lang = $("html").attr("lang");
    switch (lang) {
      case "en":
        return "https://en.yunjichina.com.cn/search.html?key=";
      case "cn":
      case "hk":
        return "https://www.yunjichina.com.cn/search.html?key=";
      default:
        return "https://en.yunjichina.com.cn/search.html?key="; // fallback
    }
  }

  // Search in header (#searchInput)
  $("#searchInput").on("keypress", function (event) {
    if (event.which === 13) { // Enter
      event.preventDefault();
      var key = $(this).val().trim();
      if (key.length > 0) {
        window.location.href = getSearchBaseUrl() + encodeURIComponent(key);
      }
    }
  });

  // Search in footer (.footer-search form)
  $(".footer-search form").on("submit", function (event) {
    event.preventDefault(); // stop default action
    var key = $(this).find("input[name='key']").val().trim();
    if (key.length > 0) {
      window.location.href = getSearchBaseUrl() + encodeURIComponent(key);
    }
  });
});
