.company-presentations-layout {
    .timeline {
        .timeline-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: .8rem;
            background: #FFF;
            box-shadow: 0 0 2rem 0 rgba(0, 0, 0, 0.04);
            padding: 2rem;
            margin-bottom: 3rem;
            transition: transform .2s ease;
            &:last-child {
                margin-bottom: 0;
            }
            &:hover {
                transform: translateY(-2px);
                .timeline-date {
                    .day,
                    .month {
                        color: #1B73C0;
                    }
                }
                .timeline-content {
                    a {
                        color: #1B73C0;
                    }
                }
            }

            .timeline-date {
                display: flex;
                flex-direction: column;
                align-items: center;
                min-width: 6rem;
                padding-right: 2rem;
                position: relative;

                &:after {
                    content: "";
                    height: 70%;
                    width: 1px;
                    background: #00000036;
                    top: 15%;
                    right: 0;
                    position: absolute;
                }
                
                .day,
                .month {
                    color: #333;
                }
                .day {
                    font-size: 3.2rem;
                    font-weight: 700;
                }
                .month {
                    font-size: 1.4rem;
                    font-weight: 400;
                    margin-top: .2rem;
                }
            }

            .timeline-content {
                flex: 1;
                margin: 0 2rem;
                a {
                    font-size: 2.5rem;
                    color: #333;
                    font-weight: 400;
                    @media (max-width: 767px) {
                        font-size: 1.8rem;
                    }
                }
            }

            .timeline-link {
                svg {
                    width: 3.4rem;
                    height: 3.4rem;
                }
            }
        }
    }
}