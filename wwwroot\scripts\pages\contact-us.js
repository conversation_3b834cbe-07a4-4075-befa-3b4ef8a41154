$(document).ready(function () {
  var language = $("html").attr("lang");
  var textFailed = "Failed to send email";
  var $sendButton = $("#sendButton");
  var supportForm = $("#contact-form");

  var pathJson = "/json/" + language + "-country-select.json";
  pathJson = window.location.origin + pathJson;

  $.getJSON(pathJson, function (data) {
    updateDropdown("demo-country", data["country-select"]);
  }).fail(function (jqxhr, textStatus, error) {
    console.error("Error fetching or parsing JSON: ", textStatus, error);
  });

  function updateDropdown(selectId, items) {
    var $selectElement = $("#" + selectId);
    $selectElement.empty();
    $.each(items, function (i, item) {
      $selectElement.append($("<option>").val(item.text).text(item.text));
    });
    $selectElement.niceSelect("update");
  }

  const rules = {
    email: (val) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
    country: (val) => val?.trim() !== "",
    company: (val) => val?.trim() !== "",
    phone: (val) => val?.trim() !== "",
    name: (val) => val?.trim() !== "",
  };

  function handleSubmit(mode) {
    return function (e) {
      e.preventDefault();
      const form = e.target;
      let valid = true;
      for (let name in rules) {
        const val = form[name]?.value?.trim();
        const errorDiv = document.getElementById("errors-" + name);

        if (!rules[name](val, form)) {
          $(errorDiv).show();
          valid = false;
        } else {
          $(errorDiv).hide();
        }
      }
      if (valid) {
        $sendButton.prop("disabled", true);
        grecaptcha.execute().then(function (token) {
          sendIPOEmail({ token, mode });
        });
      }
    };
  }

  supportForm.on("submit", handleSubmit());

  const sendIPOEmail = ({ token, mode }) => {
    var formData = new FormData(supportForm[0]);
    formData.append("language", language);
    formData.append("gRecaptchaResponse", token);
    const urlApi = `/api/ir-contact`;
    return $.ajax({
      type: "POST",
      url: urlApi,
      data: formData,
      contentType: false,
      processData: false,
      success: function (res) {
        $sendButton.prop("disabled", false);
        if (res) {
          toggleToast(
            true,
            res?.message
          );
          supportForm[0].reset();
          $("select").niceSelect("update");
        } else {
          if (res.Errors && res.Errors.length > 0) {
            const errorMessages = res.Errors.map((e) => e.message).join("<br>");
            toggleToast(true, errorMessages, "#ff0018");
          } else {
            toggleToast(true, "Failed to send. Please try again.", "#ff0018");
          }
          toggleToast(true, textFailed, "#ff0018");
        }
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error("AJAX error:", textStatus, errorThrown);
      },
    });
  };

  function toggleToast(show, message, color) {
    const toast = document.getElementById("toast");
    const toastMessage = document.getElementById("toastMessage");
    const colorItem = color ?? "#00bb2c";

    if (show) {
      toast.classList.add("show");
      toast.style.background = colorItem;
      toastMessage.textContent = message;
      setTimeout(() => toggleToast(false), 3000);
    } else {
      toast.classList.remove("show");
    }
  }

  const closeToastBtn = document.getElementById("closeToast");
  if (closeToastBtn) {
    closeToastBtn.addEventListener("click", () => toggleToast(false));
  }
});
