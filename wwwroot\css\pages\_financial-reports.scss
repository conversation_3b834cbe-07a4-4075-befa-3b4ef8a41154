
.financial-reports-layout {
    .quicklinks {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 12rem;
        padding: 0 5rem;


        @media (max-width: 499px) {
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
        }

        @media (min-width: 500px) and (max-width: 767px) {
            grid-template-columns: repeat(3, 1fr);
            gap: 4rem;
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            grid-template-columns: repeat(3, 1fr);
            gap: 4rem;
        }

        @media (min-width: 1025px) and (max-width: 1280px) {
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
        }

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;
            .box-icon {
                width: 12.4rem;
                height: 12.4rem;
                border-radius: 50%;
                background: rgba(217, 217, 217, 0.48);
                display: flex;
                justify-content: center;
                align-items: center;
            }

            a {
                color: #000;
                font-size: 1.6rem;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-top: 2.2rem;
            }
        }
    }

    .box-tool {
        margin-top: 6rem;
        .box-img {
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }

    .list-rp {
        margin-top: 12rem;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 7rem;

        @media (max-width: 500px) {
            grid-template-columns: repeat(1, 1fr);
            gap: 3rem;
        }

        @media (min-width: 501px) and (max-width: 767px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 3rem;
        }

        @media (min-width: 768px) and (max-width: 1024px) {
            grid-template-columns: repeat(3, 1fr);
            gap: 4rem;
        }

        .item {
            &:hover {
                .box-img  {
                    box-shadow: 0 .4rem .4rem 0 rgba(0, 0, 0, 0.25);
                    img
                    {
                        transform: scale(1.1);
                    }
                }

                .right {
                    .pdf-btn {
                        svg {
                            &:first-child {
                                opacity: 0;
                                visibility: hidden;
                            }
                        }
                        .svg-hover {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
            .box-img {
                overflow: hidden;
                max-height: 38rem;
                margin-bottom: 1rem;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    aspect-ratio: 296.79/394.01;
                    transition: all 0.3s ease;
                }
            }

            .box-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .left {
                .date,
                .title {
                    font-size: 1.4rem;
                    font-style: normal;
                    line-height: normal;
                }
                .date {
                    color: #00A607;
                    font-weight: 400;
                }
                .title {
                    color: #000;
                    font-weight: 700;
                }
            }

            .right {
                .pdf-btn {
                    position: relative;
                    .svg-hover {
                        position: absolute;
                        opacity: 0;
                        visibility: hidden;
                        left: 0;
                        transition: opacity .3s ease;
                    }
                }
            }
        }
    }


    .pagination {
        margin-top: 6rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;

        .pagination-wrapper {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;

            .pagination-btn {
                display: flex;
                align-items: center;
                gap: 0.8rem;
                padding: 1rem 1.6rem;
                border: 1px solid #E5E5E5;
                border-radius: 0.8rem;
                background: #fff;
                color: #666;
                text-decoration: none;
                font-size: 1.4rem;
                font-weight: 500;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #00A607;
                    color: #00A607;
                    background: rgba(0, 166, 7, 0.05);
                }

                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                    pointer-events: none;
                }

                svg {
                    width: 1.6rem;
                    height: 1.6rem;
                }
            }

            .pagination-numbers {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .pagination-number {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 4rem;
                    height: 4rem;
                    border: 1px solid #E5E5E5;
                    border-radius: 0.8rem;
                    background: #fff;
                    color: #666;
                    text-decoration: none;
                    font-size: 1.4rem;
                    font-weight: 500;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: #00A607;
                        color: #00A607;
                        background: rgba(0, 166, 7, 0.05);
                    }

                    &.active {
                        background: #00A607;
                        color: #fff;
                        border-color: #00A607;
                        cursor: default;
                    }
                }

                .pagination-dots {
                    padding: 0 0.5rem;
                    color: #999;
                    font-size: 1.4rem;
                }
            }
        }

        .pagination-info {
            color: #666;
            font-size: 1.4rem;
            font-weight: 400;
        }

        @media (max-width: 768px) {
            .pagination-wrapper {
                .pagination-btn {
                    padding: 0.8rem 1.2rem;
                    font-size: 1.2rem;

                    svg {
                        width: 1.4rem;
                        height: 1.4rem;
                    }
                }

                .pagination-numbers {
                    .pagination-number {
                        width: 3.6rem;
                        height: 3.6rem;
                        font-size: 1.2rem;
                    }
                }
            }

            .pagination-info {
                font-size: 1.2rem;
            }
        }
    }
}
