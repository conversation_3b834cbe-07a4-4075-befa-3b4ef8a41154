﻿@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Web.Common.PublishedModels;
@using Microsoft.AspNetCore.Html;

@{
	Layout = "childPageLayout.cshtml";
	var home = Model.Root();
	var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
	var languageCodeTool = "en-GB";

	if (language.Equals("en-US"))
	{
		language = "en";
		languageCodeTool = "en-GB";
	}
	else if (language.Equals("ar-SA"))
	{
		language = "ar";
		languageCodeTool = "ar-ae";
	}

	var toolSource = Model.Value("toolSource");
	var additionalText = Model.Value("additionalText");
	var pageTitle = Model.Value("pageTitle").ToString();

	var commonEmbedPage = home.DescendantsOfType("commonEmbedPage")?.FirstOrDefault() ?? null;

	var dataToolName = Model.Value<string>("dataToolName");
	var dataToolVersion = Model.Value<string>("dataToolVersion");
	var dataLazy = Model.Value<string>("dataLazy");
	var dataToolEnforcedCompanyCode = Model.Value<string>("dataToolEnforcedCompanyCode");
	var dataToolUrlBase = Model.Value<string>("dataToolUrlBase");

	if (dataToolName == "factsheet" && language.Equals("en-US"))
	{
		languageCodeTool = "english";
	}
	if (dataToolName == "factsheet" && language.Equals("ar-SA"))
	{
		languageCodeTool = "arabic";
	}
}

<div class="content-contact">
	<div class="container">
		@if (dataToolName != "")
		{

			<euroland-tool data-tool-name="@dataToolName" data-tool-version="@dataToolVersion"
				data-tool-url-base="@dataToolUrlBase" data-tool-language="@languageCodeTool">
			</euroland-tool>
		}
	</div>
</div>