﻿@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.Models.Blocks
@using Umbraco.Cms.Core.Models.PublishedContent
@using Umbraco.Cms.Web.Common.PublishedModels
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage

@{
    Layout = null;
    var home = Model.Root();
    var titleSubcribe = home?.Value<string>("titleSubcribe");
    var bgSubcribe = home?.Value<IPublishedContent>("bgSubcribe")?.Url() ?? "";
    var subcribeLink = home.Value<Link>("subcribeLink")?.Url ?? "";

    var footerLogo = home?.Value<IPublishedContent>("footerLogo")?.Url() ?? "";
    var descriptionFooter = home?.Value<string>("descriptionFooter");

    List<IPublishedElement> socialNetworkList = home?.Value<BlockListModel>("socialNetworkList")?.Select(b =>
    b.Content).ToList() ?? new
    List<IPublishedElement>();
    List<IPublishedElement> quickLinks = home?.Value<BlockListModel>("quickLinks")?.Select(b => b.Content).ToList() ?? new
    List<IPublishedElement>();

    List<IPublishedElement> listInfoFooter = home?.Value<BlockListModel>("listInfoFooter")?.Select(b => b.Content).ToList() ?? new
    List<IPublishedElement>();

    List<IPublishedElement> listQRFooter = home?.Value<BlockListModel>("listQRFooter")?.Select(b => b.Content).ToList() ?? new
    List<IPublishedElement>();

    var copyright = home?.Value<string>("copyright");
    var menuItems = home.Children().Where(x => x.IsVisible()).ToList();
    menuItems.Insert(0, home);
}

<footer class="footer">
    <div class="container-fluid">
        <div class="footer-container">
            <div class="box-ft">
                <div class="footer-left">
                        <div class="footer-cols">
                            @Html.Partial(
                            "~/Views/Menu/CustomMenu.cshtml",
                                    new ViewDataDictionary(this.ViewData) {
                                    { "menuItems", menuItems },
                                    { "isFooter", true },
                                    { "isMobile", false }
                                    })
                        </div>

                        <div class="footer-search">
                            <form method="get" action="/search.html">
                                <input placeholder="@Umbraco.GetDictionaryValue("Enter keywords")" name="key" type="text">
                                <button type="submit"></button>
                            </form>
                        </div>
                    </div>

			    <div class="footer-right">
                    <div class="footer-logo">
                        <img src="@footerLogo" alt="云迹科技">
                    </div>

                    <div class="footer-info">
                        @if (listInfoFooter != null)
                        {
                            foreach (var item in listInfoFooter)
                            {
                                var description = item.Value<string>("description");
                                var icon = item?.Value<IPublishedContent>("icon")?.Url() ?? "";

                                <div class="item">
                                    <div class="box-left">
                                        <div class="box-icon">
                                            <img src="@icon" alt="icon">
                                        </div>
                                    </div>

                                    <div class="box-right">
                                        <div class="content">
                                            @Html.Raw(description)
                                        </div>
                                    </div>
                                </div>

                            }
                        }
                    </div>

                    <div class="list-qrcode">
                        @if (listQRFooter != null)
                        {
                            foreach (var item in listQRFooter)
                            {
                                var description = item.Value<string>("description");
                                var icon = item?.Value<IPublishedContent>("icon")?.Url() ?? "";

                                <div class="item">
                                    <div class="box-qr">
                                        <img src="@icon" alt="icon">
                                    </div>

                                    <div class="content">
                                        @Html.Raw(description)
                                    </div>
                                </div>

                            }
                        }
                    </div>
                </div>
            </div>

            <div class="foot_bom" >
                @Html.Raw(copyright)
            </div>
        </div>
    </div>
</footer>



@functions {
    private static Link LinkOf(IPublishedElement i) => i?.Value<Link>("linkUrl");
    private static string TargetOf(IPublishedElement i) => string.IsNullOrEmpty(LinkOf(i)?.Target) ? "" : $" target=\"{LinkOf(i).Target}\"";

    private static string UrlOf(IPublishedElement i) => i?.Value<Link>("linkUrl")?.Url ?? "#";
    private static string Str(IPublishedElement i, string alias) => i?.Value<string>(alias) ?? "";
    private static string Img(IPublishedElement i, string alias) => i?.Value<IPublishedContent>(alias)?.Url() ?? "";

    private void RenderQuickLinks(List<IPublishedElement> list)
    {
        if (list == null || list.Count == 0) return;
        foreach (var it in list)
        {
        var title = Str(it, "title");
        var cls = Str(it, "customClass");
        var target = TargetOf(it);
        <a href="@UrlOf(it)" class="footer-quick-link @cls" @Html.Raw(target) title="@title">@title</a>
        }
    }

    private void RenderSocialNetwork(List<IPublishedElement> list)
    {
        if (list == null || list.Count == 0) return;
        foreach (var it in list)
        {
        var title = Str(it, "title");
        var target = TargetOf(it);
        <li class="social-item">
            <a href="@UrlOf(it)" class="social-link" title="@title" @Html.Raw(target)>
            <img src="@Img(it, "photo")" alt="@title" />
            </a>
        </li>
        }
    }
}