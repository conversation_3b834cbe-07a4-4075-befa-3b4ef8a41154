html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  line-height: 1.5;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

a {
  text-decoration: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/******** Not edit in here *********/
html,
body {
  font-size: 16px;
  scroll-behavior: smooth;
  line-height: 1.5;
}

html[lang="cn"],
html[lang="hk"] {
  body {
    font-family: "Microsoft YaHei", "Microsoft YaHei UI", sans-serif;
    font-weight: normal;
    overflow-x: hidden;
    color: $colorText;
  }
}

html[lang="en"] {
  body {
    font-family: Arial, sans-serif;
    font-weight: normal;
    overflow-x: hidden;
    color: $colorText;
  }
}

// CSS scrollbar
// html {
//   // scroll-padding-top: 13rem;
//   &::-webkit-scrollbar {
//     width: 0.8rem;
//   }

//   &::-webkit-scrollbar-track {
//     -webkit-box-shadow: inset 0 0 0.5rem grey;
//     box-shadow: inset 0 0 0.5rem grey;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: $color-primary;
//     border-radius: 1rem;
//   }

//   &::-webkit-scrollbar-thumb:hover {
//     background: #082b45;
//   }
// }

html {
  font-size: 54%;

  @media (min-width: 31.25rem) and (max-width: 61.938rem) {
    //991px
    font-size: 54%;
  }

  @media (min-width: 62rem) and (max-width: 64rem) {
    //1024px
    font-size: 55%;
  }

  @media (min-width: 65rem) and (max-width: 80rem) {
    //1280px
    font-size: 58%;
  }

  @media (min-width: 81rem) and (max-width: 90rem) {
    //1366px
    font-size: 60%;
  }

  @media (min-width: 91rem) {
    //1600px up
    font-size: 62.5%;
  }
}

.inner-content {
  padding: 0 0 10rem;
}

.container {
  padding: 0 15px;
  width: 100%;
  margin: 0 auto;
  z-index: 1;
  position: relative;

  @media (min-width: 48px) {
    //768px
    max-width: 100rem;
  }

  @media (min-width: 62rem) {
    //992px
    max-width: 110rem;
  }

  @media (min-width: 75rem) {
    //1200px
    max-width: 140rem;
  }

  // @media (min-width: 87.5rem) {
  //   //1400px
  //   max-width: 129rem;
  // }
  // @media (min-width: 93.75rem) {
  //   //1500px
  //   max-width: 140rem;
  // }
}

h2.page-title {
  color: #00201b;
  font-size: 4.5rem;
  font-style: normal;
  font-weight: 300;
  line-height: 1.37;
  @media screen and (max-width: 1440px) {
    font-size: 4.2rem;
  }
  @media screen and (max-width: 1280px) {
    font-size: 3.5rem;
  }
  @media screen and (max-width: 991px) {
    font-size: 3rem;
  }
}
img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
}

.nice-select {
  float: right;
  border-bottom: 0.1rem solid #e0e0e0 !important;
  border: none;
  border-radius: 0;
  color: #44444b;
  font-size: 1.8rem;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding-right: 3rem;
  padding-left: 1rem;
  &::after {
    border-bottom: 1px solid #44444b;
    border-right: 1px solid #44444b;
    height: 12px;
    width: 12px;
    right: 2rem;
    top: 36%;
  }
  .list {
    width: 100%;
    max-height: 350px;
    overflow-y: auto;
  }
}
.form-select {
  --bs-form-select-bg-img: none;
}

@mixin hover-image {
  position: relative;
  font-size: 0;
  overflow: hidden;
  cursor: pointer;
  transition: all 1.25s cubic-bezier(0, 0, 0.2, 1);

  &:hover {
    img {
      transform: scale3d(1.1, 1.1, 1.1);
    }
  }

  img {
    width: 100%;
    height: auto;
    transition: all 1.25s cubic-bezier(0, 0, 0.2, 1);
  }
}

@mixin transition {
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

@mixin hover-underline {
  position: relative;
  @include transition();

  &:after {
    position: absolute;
    top: calc(100% + 0.2rem);
    left: 0;
    display: block;
    content: "";
    width: 0;
    height: 1px;
    background-color: #fff;

    @include transition();
  }

  &:hover {
    &::after {
      width: 100%;
    }
  }
}

a {
  @include transition();
}

.listing-documents-layout,
.financial-reports-layout {
  margin-top: 20rem;
}

.header-common {
  text-align:center;
  margin-bottom:3rem;
  h1.title {
    color: #000;
    font-family: Arial;
    font-size: 4.8rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -1px;
    padding-bottom: 7rem;
    @media (max-width: 767px) {
        font-size: 3rem;
    }
  }

  .rule {
    height: .5rem;
    max-width: 100%;
    margin: 0 auto 4.8rem;
    background: #00A607;
  }
}