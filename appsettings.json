{"$schema": "appsettings-schema.json", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Error", "System": "Error"}}}, "ConnectionStrings": {"umbracoDbDSN": "server=bingo;database=umb-insilico-medicine-ir;user id=test;password='123456';TrustServerCertificate=True;", "umbracoDbDSN_ProviderName": "Microsoft.Data.SqlClient"}, "GoogleRecaptcha": {"SiteKey": "SiteKey", "SecretKey": "<PERSON><PERSON><PERSON>"}, "Umbraco": {"CMS": {"Global": {"Id": "6e661cf1-a154-41a6-b975-b1dd7a484fef", "SanitizeTinyMce": true, "TimeOut": "00:59:00"}, "Hosting": {"LocalTempStorageLocation": "EnvironmentTemp"}, "Examine": {"LuceneDirectoryFactory": "TempFileSystemDirectoryFactory"}, "Content": {"AllowEditInvariantFromNonDefault": true, "Error404Collection": [{"Culture": "default", "ContentKey": "3a78ef03-fd98-43f6-a4f9-985aeeba7aaf"}], "Notifications": {"Email": "<EMAIL>"}, "ContentVersionCleanupPolicy": {"EnableCleanup": true}}, "Unattended": {"UpgradeUnattended": true}, "Security": {"BackOfficeHost": "https://dev.vn.euroland.com:8123"}, "WebRouting": {"UmbracoApplicationUrl": "https://dev.vn.euroland.com:8123"}, "Logging": {"MaxLogAge": "2.00:00:00"}}, "Storage": {"AzureBlob": {"Media": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=eaumbmedia;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "umb-insilico-medicine-ir"}}}}}