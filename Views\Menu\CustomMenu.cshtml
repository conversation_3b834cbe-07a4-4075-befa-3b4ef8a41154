@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@{
  var home = Model.Root();
  List<IPublishedContent> menuItems = (List<IPublishedContent>)ViewData["menuItems"];
  bool isFooter = false;
  bool isMobile = false;
  if (home == Model)
  {
    isFooter = (bool)ViewData["isFooter"];
  }
  isMobile = (bool)ViewData["isMobile"];
  generateMenu(menuItems, false, false, isFooter, isMobile);
}

@functions {
  public bool isHomePage(IPublishedContent menuItem)
  {
    return menuItem.ContentType.Alias == "homePage";
  }

  public bool isRedirectPage(IPublishedContent menuItem)
  {
    return (menuItem.ContentType.Alias == "redirectPage" || menuItem.ContentType.Alias == "redirectLandingPage");
  }

  public bool hasAnyActiveSubMenu(List<IPublishedContent> items)
  {
    foreach (var item in items)
    {
      if (isInCurrentPageTree(item))
      {
        return true;
      }
    }
    return false;
  }

  public bool isInCurrentPageTree(IPublishedContent menuItem)
  {
    if (isHomePage(menuItem) && !isHomePage(Model))
    {
      return false;
    }

    return Model.AncestorsOrSelf().Where(x => x.Url().Equals(menuItem.Url())).Any();
  }
  public bool hasAnyChildrenOfSuitableType(IPublishedContent menuItem)
  {
    if (isHomePage(menuItem))
    {
      return false;
    }

    return (menuItem.Children().Where(x => x.IsVisible())
    .Where(x => x.ContentType.Alias != "commonTabcontentPage")
    .Where(x => x.ContentType.Alias != "listingDocumentsItem")
    .Where(x => x.ContentType.Alias != "companyPresentationsPage")
    .Where(x => x.ContentType.Alias != "financialReportsPage")
    .Where(x => x.ContentType.Alias != "bodPage")
    .Where(x => x.ContentType.Alias != "committeeCompositionPage")
    .Where(x => x.ContentType.Alias != "corporateGovernanceDocumentsPage")


    .Any());
  }

  public void generateMenu(List<IPublishedContent> menuItems, bool isSubMenu, bool hasActiveSubMenu, bool isFooter, bool
  isMobile)
  {
    bool hasAnyChildren = false;
    var menuResponsive = isMobile == true ? "menu-responsive" : "menu-pc";
    <ul class="@menuResponsive ul-menu">
      @{
        var existing = false;
      }
      @foreach (var item in menuItems) //lv 1
      {
        if (item.Level > 3)
        {
          continue;
        }
        bool isRedirect = isRedirectPage(item);
        string href = item.Url();
        string target = "";

        if (isHomePage(item))
        {
          href = item.Value<Link>("homePageLogoUrl")?.Url ?? "";
        }

        hasAnyChildren = hasAnyChildrenOfSuitableType(item);
        if (isRedirect && item.HasValue("redirectLink"))
        {
          Umbraco.Cms.Core.Models.Link redirectLink = item.Value<Link>("redirectLink");
          if (redirectLink != null)
          {
            href = redirectLink.Url;
            target = redirectLink.Target;
          }
        }
        /*make special url for Media Categories*/
        if (item.IsDocumentType("mediaCategoryPage"))
        {
          var itemName = StrConvert(item.Name.ToString());
          href = item.Parent.Url() + "?tab=" + @itemName + "&page=1";
        }

        /* add '#' into hyperlink */
        var parent = item.Parent;
        var linkUrl = item.Url().ToString();
        var tabName = "";

        if (parent != null)
        {
          if (parent.IsDocumentType("commonTabcontentPage"))
          {
            var itemName = StrConvert(item.Name.ToString());
            href = item.Parent.Url() + "#" + @itemName;
          }
        }
        var hassub = hasAnyChildren ? "hassub multi-level" : "";
        var activeClass = isInCurrentPageTree(item) ? "active" : "";
        var pageTitle = item.Value("pageTitle") != null ? item.Value<string>("pageTitle") : "";
        var classPageName = item.Name().Replace(" ", "-").Replace("'", "-").Replace(",", "").Replace("&", "-");
        <li class="menu-item @hassub @activeClass <EMAIL> menu-@classPageName @item.Id">
          <span class="toggle-sub"></span>
          <a href="@href" class="menu" title=" @pageTitle" target="@target">
            @if (isMobile == true)
            {
              <span>@Html.Raw(pageTitle)</span>
            }
            else
            {
              @Html.Raw(pageTitle)
            }
          </a>
          <i></i>


          @if (hasAnyChildren)
          {
            <div class="open-sub">
              <i class="mToggle"></i>
            </div>
            @if (isMobile == false)
            {
              @:<div class="wrap-sub-menu">
              }

              @if (hasAnyChildren)
              {
                hasActiveSubMenu = hasAnyActiveSubMenu(item.Children.ToList());
                generateMenu(item.Children.Where(x => x.IsVisible()).ToList(), true, hasActiveSubMenu, isFooter, isMobile)
                ;
              }

              @if (isMobile == false)
              {
                @:</div>
            }
          }
        </li>
      }
    </ul>
  }
  private static string StrConvert(string name)
  {
    name = name.Replace(" ", "-");
    name = name.Replace("!", "");
    name = name.Replace("@", "");
    name = name.Replace("#", "");
    name = name.Replace("$", "");
    name = name.Replace("%", "");
    name = name.Replace("^", "");
    name = name.Replace("&", "");
    name = name.Replace("*", "");
    name = name.Replace("(", "");
    name = name.Replace(")", "");
    name = name.Replace("=", "");
    name = name.Replace("+", "");
    name = name.Replace("|", "");
    name = name.Replace("/", "");
    name = name.Replace("?", "");
    name = name.Replace(">", "");
    name = name.Replace("<", "");
    name = name.Replace(".", "");
    name = name.Replace(",", "");
    return name.ToLower();
  }
}