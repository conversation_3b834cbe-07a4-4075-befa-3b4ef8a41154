// document.addEventListener("DOMContentLoaded", function () {
//   document.querySelectorAll(".footer-wechat .icon").forEach(function (icon) {
//     icon.addEventListener("click", function () {
//       const parent = this.closest(".footer-wechat");
//       if (parent) {
//         parent.classList.toggle("close");
//       }
//     });
//   });

//   document
//     .querySelectorAll(
//       ".site-footer .menu-item.hassub > a, .site-footer .menu-item.hassub .open-sub"
//     )
//     .forEach(function (link) {
//       link.addEventListener("click", function (e) {
//         e.preventDefault();
//         e.stopPropagation();
//         const menuItem = this.closest(".site-footer .menu-item.hassub .menu");
//         if (menuItem) {
//           menuItem.classList.toggle("open");
//         }
//       });
//     });
// });



document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll(".menu-item.hassub .toggle-sub").forEach(function (btn) {
    btn.addEventListener("click", function (e) {
      e.preventDefault();
      e.stopPropagation();

      const li = this.closest(".menu-item.hassub");
      li.classList.toggle("open");
    });
  });
});