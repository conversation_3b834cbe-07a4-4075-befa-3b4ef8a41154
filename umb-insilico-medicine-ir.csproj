<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>umb_insilico_medicine_ir</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LigerShark.WebOptimizer.Core" Version="3.0.433" />
    <PackageReference Include="Our.Umbraco.TheDashboard" Version="14.0.1" />
    <PackageReference Include="Umbraco.Cms" Version="14.3.4" />
    <PackageReference Include="Umbraco.StorageProviders.AzureBlob" Version="14.1.0" />
    <PackageReference Include="Umbraco.StorageProviders.AzureBlob.ImageSharp" Version="14.1.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Opt-in to app-local ICU to ensure consistent globalization APIs across different platforms -->
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" Condition="$(RuntimeIdentifier.StartsWith('linux')) or $(RuntimeIdentifier.StartsWith('win')) or ('$(RuntimeIdentifier)' == '' and !$([MSBuild]::IsOSPlatform('osx')))" />
  </ItemGroup>

  <PropertyGroup>
    <!-- Razor files are needed for the backoffice to work correctly -->
    <CopyRazorGenerateFilesToPublishDirectory>true</CopyRazorGenerateFilesToPublishDirectory>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Remove RazorCompileOnBuild and RazorCompileOnPublish when not using ModelsMode InMemoryAuto -->
    <RazorCompileOnBuild>false</RazorCompileOnBuild>
    <RazorCompileOnPublish>false</RazorCompileOnPublish>
  </PropertyGroup>

</Project>
