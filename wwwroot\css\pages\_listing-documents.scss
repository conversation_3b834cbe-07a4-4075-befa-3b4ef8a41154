.listing-documents-layout {

    .listings {
        display: flex;
        flex-direction: column;
        gap: 5rem;
        padding: 0 .8rem 6rem;
        margin-top: 10rem;
    }

    .listing-item{
        display: flex;
        justify-content: space-between;
        padding: 2.8rem 2.6rem;
        background: #fff;
        border: .1rem dotted var(--border-dot);
        border-radius: 1rem;
        background: #FFF;
        box-shadow: 0 .4rem .4rem 0 rgba(0, 0, 0, 0.25);
        transition: transform .12s ease, box-shadow .12s ease;
        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 1.4rem 1.8rem rgba(0, 0, 0, 0.25);
            .listing-desc {
                color: #00A607;
            }

            .listing-icon { 
                .pdf-btn {
                    svg {
                        &:first-child {
                            opacity: 0;
                            visibility: hidden;
                        }
                    }
                    .svg-hover {
                        opacity: 1;
                        visibility: visible;
                    }
                }
            }
        }

        .listing-date {
            color: #434343;
            text-align: justify;
            font-size: 1.8rem;
            font-style: normal;
            font-weight: 400;
            line-height: 1.5;
        }

        .listing-desc {
            padding: 0 1.2rem;
            color: #000;
            text-align: justify;
            font-size: 1.8rem;
            font-style: normal;
            font-weight: 400;
            line-height: 1.5;
            @media (max-width: 767px) {
                text-align: left;
            }
        }

        .listing-icon {
            display:flex;
            justify-content:center;
            align-items:center;

            .pdf-btn {
                position: relative;
                .svg-hover {
                    position: absolute;
                    opacity: 0;
                    visibility: hidden;
                    left: 0;
                    transition: opacity .3s ease;
                }
            }
        }
    }
}