﻿@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.Models.Blocks;
@using Umbraco.Cms.Web.Common.PublishedModels;
@using Umbraco.Cms.Core.Models.PublishedContent;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage

@{
  Layout = null;
  var home = Model.Root();
  string companyLogo = home.Value<IPublishedContent>("companyLogo")?.Url() ?? "";
  string mobileLogo = home.Value<IPublishedContent>("mobileLogo")?.Url() ?? "";

  var homePageLogoUrl = home.HasProperty("homePageLogoUrl") && home.HasValue("homePageLogoUrl") ?
  home.Value<Link>("homePageLogoUrl").Url : home.Url();
  var menuItems = home.Children().Where(x => x.IsVisible()).ToList();
  var count = 0;
  var unit = "0px";
  menuItems.Insert(0, home);

  var lang = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
  var searchLang = "en";
  if (lang.ToLower() == "zh-cn")
  {
    searchLang = "cn";
  }
  else if (lang.ToLower() == "zh-hk")
  {
    searchLang = "hk";
  }
  else
  {
    searchLang = "en";
  }

  var linkContact = home.Value<Link>("linkContact")?.Url;

  // Setting value
  var logoWidth = home.HasProperty("logoWidth") && home.HasValue("logoWidth") ? home.Value<decimal>("logoWidth") : 6;
  var headerHeight = home.HasProperty("headerHeight") && home.HasValue("headerHeight") ?
  home.Value<decimal>("headerHeight") : 7;
  var logoWidthStyle = logoWidth + unit;
  var headerHeightStyle = headerHeight + unit;
  var backgroundHeader = home.Value<string>("backgroundHeader");
  var backgroundMenu = home.Value<string>("backgroundMenu");
  var colorMenu = home.Value<string>("colorMenu");
  var headerSticky = home.HasProperty("headerSticky") && home.HasValue("headerSticky")
  ? home.Value<string>("headerSticky").ToLower().Replace(" ", "-").Replace("'", "-") : "base";
  var headerMobile = home.HasProperty("headerMobile") && home.HasValue("headerMobile")
  ? home.Value<string>("headerMobile").ToLower().Replace(" ", "-").Replace("'", "-") : "justify";
  var menuMobileStyle = home.HasProperty("menuMobileStyle") && home.HasValue("menuMobileStyle")
  ? home.Value<string>("menuMobileStyle").ToLower().Replace(" ", "-").Replace("'", "-") : "dropdown";
  var menuMobileLocation = home.HasProperty("menuMobileLocation") && home.HasValue("menuMobileLocation")
  ? home.Value<string>("menuMobileLocation").ToLower().Replace(" ", "-").Replace("'", "-") : "right";
  var search = home.HasProperty("search") && home.HasValue("search") ? home.Value<string>("search").ToLower().Replace(" ",
  "-").Replace("'", "-") : "false";
  var language = home.HasProperty("language") && home.HasValue("language")
  ? home.Value<string>("language").ToLower().Replace(" ", "-").Replace("'", "-") : "false";

  var quickLinkHeader = home.Value<BlockListModel>("quickLinkHeader")?.Select(block => block.Content).ToList() ?? new
  List<IPublishedElement>();

  // Set default value
  if (string.IsNullOrEmpty(backgroundHeader)) { backgroundHeader = "transarent"; }
  if (string.IsNullOrEmpty(backgroundMenu)) { backgroundMenu = "#202020"; }
  if (string.IsNullOrEmpty(colorMenu)) { colorMenu = "#02A333"; }
  if (string.IsNullOrEmpty(headerSticky)) { headerSticky = "none"; }
}

<div
  class="header @headerSticky header-mobile-@headerMobile menu-mobile-@menuMobileStyle menu-mobile-@menuMobileLocation">

  <div class="row-header">
    <div class="container-fluid">
      <a href="@homePageLogoUrl" class="brand-logo" title="home">
        <img src="@companyLogo" class="logo logo-pc" alt="logo" />
      </a>

      <div class="wrap-right">
        <div class="wrap-top">
          <a href="@linkContact" class="btn-contact">@Umbraco.GetDictionaryValue("Contact Us")</a>
        </div>
        <div class="wrap-bottom">
          <div class="menu-header">
            @Html.Partial(
            "~/Views/Menu/CustomMenu.cshtml",
                        new ViewDataDictionary(this.ViewData) {
                        { "menuItems", menuItems },
                        { "isFooter", false },
                        { "isMobile", false }
                        })
          </div>

          <div class="wrap-tools">
            <div class="wrap-search">
              @{
                generateSearch(search);
              }
            </div>

            <div class="wrap-lang lang-pc">
              @{
                generateLanguage(language, home);
              }
            </div>

            <div class="off-canvas">
              @{
                generateMenuOffCanvas();
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="menu-mobile">
    <div class="container">
      @Html.Partial(
      "~/Views/Menu/CustomMenu.cshtml",
            new ViewDataDictionary(this.ViewData) {
            { "menuItems", menuItems },
            { "isFooter", false },
            { "isMobile", true }
            })

      <a href="javascript:;" class="serClose"></a>      

      @* <div class="wrap-lang lang-mobile">
        @{
          generateLanguage(language, home);
        }
      </div> *@
    </div>
  </div>
</div>

<div class="search-panel">
  <a href="javascript:;" class="serClose"></a>
  <div class="serBox">
    <h6>@Umbraco.GetDictionaryValue("Search")</h6>
    <div class="serIntBox">
      <form method="get" action="">
        <input id="searchInput" name="key" type="text">
      </form>
    </div>
    <ul class="serul clearfix">
      <li>@Umbraco.GetDictionaryValue("Enter keywords")</li>
    </ul>
  </div>
</div>

@functions {

  public bool isRedirectPage(IPublishedContent menuItem)
  {
    return (menuItem.ContentType.Alias == "redirectPage" || menuItem.ContentType.Alias == "redirectLandingPage");
  }

  private void generateMenuOffCanvas()
  {
    <div class="btn-menu-offcanvas">
      <div class="mOpenBtn"><i></i><i></i><i></i><i></i></div>
    </div>
  }

  private void generateLanguage(string language, IPublishedContent home)
  {
    if (language.Equals("true"))
    {
      <div class="language-wrap">
        <div class="language">
          <img src="/media/nmdby4l2/global.svg" alt="icon">
          <div class="lang-name">
            @foreach (var (culture, infos) in home.Cultures)
            {
              <a class="@(home.Url() == home.Url(culture) ? "active" : "")" href="@(Model.Url(culture))">
                @Umbraco.GetDictionaryValue(("Language.") +
                culture)
      </a>
            }
            <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
              <path
                d="M6.79145 8.41329C6.88594 8.41329 6.97649 8.37525 7.04264 8.30775L10.0909 5.19753C10.2269 5.0588 10.2246 4.83611 10.0859 4.70015C9.94714 4.5642 9.72444 4.56643 9.58849 4.70516L6.79145 7.55908L3.9944 4.70516C3.85845 4.56642 3.63578 4.56415 3.49703 4.70015C3.35828 4.83613 3.35604 5.0588 3.49202 5.19753L6.54025 8.30775C6.6064 8.37525 6.69694 8.41329 6.79145 8.41329Z"
                fill="white" />
            </svg>
          </div>
        </div>
        <div class="lang-list">
          @foreach (var (culture, infos) in home.Cultures)
          {
            if (home.Url() != home.Url(culture))
            {
              <a href="@(Model.Url(culture))">
                @Umbraco.GetDictionaryValue(("Language.") +
                culture)
    </a>
        }
          }
        </div>
      </div>
    }
  }

  private void generateSearch(string search)
  {
    if (search == "true")
    {
      <div class="search-btn">
        <img class="icon-search-img pc" src="/media/fmbpvf4n/ser_ico.png" alt="icon-search">
        <img class="icon-search-img mobile" src="/media/u1fbbgrq/serico_b.svg" alt="icon-search">
      </div>
    }
  }
}