.site-header {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  height: 13.4rem;
  background-size: 100%;
  background-color: #fff;
  z-index: 15;
  transition: all 0.6s;
  @include transition();

  .container-fluid {
    width: 100%;
    height: 100%;
    padding: 0 10.5rem;

    @media (max-width: 1279px) {
      margin: 0;
      padding: 0 1.5rem;
      width: calc(100% - 3rem) !important;
    }
  }

  @media (max-width: 1279px) {
    height: 5.5rem;
  }

  &.sticky {
    height: 7.6rem;
    box-shadow: rgba(0, 0, 0, 0.08) 0px 3px 5px;

    .wrap-top {
      display: none;
    }

    .wrap-bottom {
      transform: translateY(0);
      height: 100%;
    }

    .header::after {
      opacity: 0;
      visibility: hidden;
    }

    .brand-logo {
      max-width: 12.8rem;
    } 

    @media (max-width: 1279px) {
      height: 5.6rem;
    }
  }
}

body .header {
  height: 100%;

  .brand-logo {
    max-width: 27.5rem;
  }

  .wrap-right {
    width: calc(100% - 27.5rem);
    height: 100%;

    @media (max-width: 1279px) {
      width: auto;
    }
  }

  &:after {
    display: block;
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 39%;
    height: 40%;
    background: linear-gradient(90deg, rgba(28, 115, 192, 0.00) 0%, #1B73C0 100%);
    @include transition();
  }

  .wrap-top {
    display: flex;
    justify-content: flex-end;
    transform: translateY(13px);
    @include transition();
  }

  .btn-contact {
    display: flex;
    width: fit-content;
    height: 3rem;
    padding: 4px 12px;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
    color: #FFF;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    border-radius: 4px;
    background: #3FBABC;

    &:hover {
      background: darken(#3FBABC, 10%);
      text-decoration: none;
      color: #FFF;
    }
  }

  .wrap-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60%;
    transform: translateY(23px);
    @include transition();
  }

  .wrap-tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
  }

  .wrap-lang.lang-pc,
  .wrap-lang.lang-mobile {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1.5rem;

    .lang-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 6px;
      padding: 1rem 1.5rem;
      border: 1px solid #fff;

      a {
        display: flex;
        align-items: center;
        gap: 5px;
  
        span {
          color: #fff;
          font-size: 1.6rem;
          font-weight: 400;
          line-height: 1.5;
        }
  
        &.active {
          display: none;
        }
      }
    }

    .lang-list {
      position: absolute;
      top: 100%;
      right: 0;
      width: max-content;
      display: flex;
      flex-direction: column;
      opacity: 0;
      visibility: hidden;
      @include transition();

      a {
        display: block;
        font-size: 1.6rem;
        font-weight: 400;
        color: #0a434d;
        line-height: 1.5;
        padding: 0.5rem 2.4rem;
        text-align: center;
        border-bottom: 1px solid #efb347;
        background-color: #fff;

        &.active {
          color: $color-primary;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }

    &:hover {
      .lang-list {
        top: calc(100% + 2rem);
        opacity: 1;
        visibility: visible;
      }
    }

    @media (max-width: 1279px) {
      margin-left: 0.5rem;
    }
  }

  .wrap-lang {
    margin-left: 3.4rem;

    .language {
      border-radius: 4px;
      background: #1B73C0;
      padding: 7px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      gap: 5px;

      a {
        font-size: 12px;
        font-weight: 400;
        color: #fff;

        &:not(.active) {
          display: none;
        }
      }
    }
    .lang-list {
      display: none;
    }
  }

  .wrap-search {
    cursor: pointer;

    .search-btn {
      font-size: 0;

      img.pc {
        display: block;
      }

      img.mobile {
        display: none;
      }

      @media (max-width: 1279px) {
        width: 30px;

        img.pc {
          display: none;
        }

        img.mobile {
          display: block;
        }
      }
    }
  }

  .row-header {
    height: 100%;
    .container-fluid {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      z-index: 2;
    }

    .logo {
      width: 100%;
    }

    .row {
      justify-content: space-between;
      align-items: center;

      @media (min-width: 1025px) {
        justify-content: center;
      }
    }

    .off-canvas {
      display: none;

      @media (max-width: 1279px) {
        display: block;
      }
    }
  }

  &.header-mobile-center {
    @media (max-width: 575px) {
      .brand-logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  &.base-sticky {
    &.headroom--unpinned,
    &.headroom--pinned.headroom--not-top {
      position: fixed;
      top: 0;
      width: 100%;
      background-color: #fff;

      &:after {
        display: none;
      }

      .menu-header>.ul-menu>li>a {
        color: #000;
      }

      .menu-header>.ul-menu>li>.open-sub {
        border-bottom: 1px solid #000;
        border-right: 1px solid #000;
      }

      .lang-status {
        border: 1px solid #000;

        a span {
          color: #000;
        }
        svg path {
          stroke: #000;
        }
      }

      .menu-header .multi-level.level-2>.wrap-sub-menu>.ul-menu {
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      }

      .menuicon-label {
        span,
        &:after,
        &:before {
          background-color: #000;
        }
      }
    }
  }

  &.dynamic-sticky {
    &.headroom--unpinned,
    &.headroom--pinned.headroom--not-top {
      position: fixed;
      top: 0;
      width: 100%;
      background-color: #fff;

      &:after {
        display: none;
      }

      .menu-header>.ul-menu>li>a {
        color: #000;
      }

      .menu-header>.ul-menu>li>.open-sub {
        border-bottom: 1px solid #000;
        border-right: 1px solid #000;
      }

      .lang-status {
        border: 1px solid #000;

        a span {
          color: #000;
        }
        svg path {
          stroke: #000;
        }
      }

      .menu-header .multi-level.level-2>.wrap-sub-menu>.ul-menu {
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      }

      .menuicon-label {
        span,
        &:after,
        &:before {
          background-color: #000;
        }
      }
    }

    &.headroom--pinned {
      position: fixed;
      top: 0;
      width: 100%;
      transform: translateY(0);
      transition: all 0.75s ease-in-out;

      &.headroom--top {
        position: absolute;
      }
    }
 
    &.headroom--unpinned {
      position: fixed;
      top: 0;
      width: 100%;
      transform: translateY(-100%);
      transition: all 0.75s ease-in-out;
    }
  }

  .mOpenBtn {
    width: 26px;
    height: 20px;
    position: absolute;
    right: -30px;
    top: 50%;
    margin-top: -9px;
    overflow: hidden;
    cursor: pointer;

    i {
      width: 30px;
      height: 2px;
      margin-bottom: 4px;
      background: #1b73c0;
      display: block;

      &:nth-child(2) {
        width: 21px;
      }

      &:nth-child(4) {
        width: 10px;
      }
    }
  }

  @media (max-width: 1279px) {
    height: 5.6rem;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: rgba(0, 0, 0, 0.08) 0px 3px 5px;

    .wrap-top {
      display: none;
    }

    .wrap-bottom {
      transform: translateY(0);
      height: 100%;
    }

    .header::after {
      opacity: 0;
      visibility: hidden;
    }

    .brand-logo {
      max-width: 12.8rem;
    }

    &:after {
      display: none;
    }
  }
}

.menuicon-label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  width: 22px;
  height: 22px;

  &:after,
  &:before {
    content: "";
  }

  span,
  &:after,
  &:before {
    display: block;
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 2px;
    transform: translateY(-50%);
    background: $color-primary;
    transition: all 0.36s;
  }

  span {
    .active-menu & {
      opacity: 0;
    }
  }

  &::before {
    margin-top: -6px;

    .active-menu & {
      transform: translateY(-50%) rotate(45deg);
      margin-top: 0;
    }
  }

  &::after {
    margin-top: 6px;

    .active-menu & {
      transform: translateY(-50%) rotate(-45deg);
      margin-top: 0;
    }
  }
}

body.open-menu {
  .menuicon-label {
    span,
    &:after,
    &:before {
      // background-color: #000;
    }
  }
}

.background-opacity {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  z-index: 3;

  body.open-search &,
  body.open-menu & {
    opacity: 1;
    visibility: visible;
  }
}

.serClose {
    display: inline-block;
    height: 52px;
    width: 52px;
    position: fixed;
    right: 2vw;
    top: 2vw;
    z-index: 900;
    cursor: pointer;
    background: url('/media/qmrcucm0/close_w.svg') center center / 28px no-repeat;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(255, 255, 255, 0.6);
    border-image: initial;
    border-radius: 50%;
    transition: 0.5s ease-in-out;
  }

  .serBox {
    position: absolute;
    left: 0px;
    width: 100%;
    top: 50%;
    transform: translateY(-68%);
  }

.search-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(27, 115, 192, 0.95);
  opacity: 0;
  visibility: hidden;
  z-index: 10;

  @include transition();

  h6 {
    font-size: 5.8rem;
    line-height: 1.3;
    margin-bottom: 4.5rem;
    display: block;
    text-align: center;
    color: rgb(255, 255, 255);
    letter-spacing: 1.5rem;
  }

  .serIntBox {
    max-width: 90%;
    width: 120rem;
    margin: 0px auto;
  }

  input {
    font-size: 2rem;
    line-height: 5.4;
    color: rgb(255, 255, 255);
    height: 11rem;
    padding-left: 5.2rem;
    width: 100%;
    border-top: 1px solid rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.6);
    background: transparent;
    outline: none;
    border-left: none;
    border-right: none;
  }

  .serul.clearfix {
    font-size: 16px;
    line-height: 2;
    color: rgb(255, 255, 255);
    padding-left: 1.8rem;
    position: relative;
    max-width: 90%;
    width: 120rem;
    margin: 5rem auto 0px;

    li {
      &:after {
        content: "";
        width: 5px;
        height: 5px;
        position: absolute;
        left: 0px;
        top: 15px;
        background: rgb(255, 255, 255);
        border-radius: 50%;
      }
    }
  }

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

.menu-mobile {
  .serClose {
    height: 40px;
    width: 40px;
    top: 15px;
    right: 15px;
  }
}
