﻿$(document).ready(function () {
  $("select").niceSelect();

  function setupToggle(container, itemClass, tableClass) {
    $(container).on("click", itemClass, function () {
      var target = $($(this).data("target"));

      $(container)
        .find(tableClass)
        .not(target)
        .slideUp(300)
        .prev(itemClass)
        .removeClass("active");

      target.slideToggle(300);

      $(this).toggleClass("active");
    });
  }

  setupToggle(
    ".statement-section__list",
    ".statement__body",
    ".statement__answer"
  );
  setupToggle(
    ".news_section_list",
    ".notices__list__header",
    ".notices__list__text"
  );
    setupToggle(
    ".bod-section__list",
    ".bod__body",
    ".bod__desc"
  );

  document.getElementById("news-detail-back")?.addEventListener("click", () => {
    window.history.back();
  });

  $(".breadcrumb__item").first().addClass("active");

  $(".breadcrumb__item").each(function () {
    var currentPath = window.location.pathname;
    var breadcrumbPath = $(this).attr("href");

    if (breadcrumbPath === currentPath) {
      $(".breadcrumb__item").removeClass("active");
      $(this).addClass("active");
    }
  });

  $(".breadcrumb__item").on("click", function () {
    $(".breadcrumb__item").removeClass("active");
    $(this).addClass("active");
  });

  $(".popup-bod").on("click", function () {
    var popupId = $(this).closest(".popup-bod").data("popup");
    var $popup = $("#" + popupId);
    $popup.fadeIn();
    $(".background-opacity").addClass("active");
  });

  $(".btn-bod-close").on("click", function () {
    $(this).closest(".popup-overlay-bod").fadeOut();
    $(".background-opacity").removeClass("active");
  });

  var $root = $(".map-content");

  $root.find(".popup-map-item").hide();

  function openPopup($popup) {
    // Ẩn mọi popup khác trước khi mở cái mới
    $root
      .find(".popup-map-item:visible")
      .not($popup)
      .stop(true, true)
      .fadeOut(150);
    $popup.stop(true, true).fadeIn(150);
  }

  function closePopup($popup) {
    $popup.stop(true, true).fadeOut(150);
  }

  $root.on("click", ".map-pin > img", function (e) {
    e.preventDefault();
    e.stopPropagation();
    var $pin = $(this).closest(".map-pin");
    var $popup = $pin.children(".popup-map-item");
    if ($popup.length) openPopup($popup);
  });

  $root.on("click", ".btn-map-close", function (e) {
    e.preventDefault();
    e.stopPropagation();
    closePopup($(this).closest(".popup-map-item"));
  });

  $(document).on("click", function (e) {
    if ($(e.target).closest(".popup-map-item, .map-pin > img").length === 0) {
      $root.find(".popup-map-item:visible").stop(true, true).fadeOut(150);
    }
  });

  $(document).on("keyup", function (e) {
    if (e.key === "Escape") {
      $root.find(".popup-map-item:visible").stop(true, true).fadeOut(150);
    }
  });

});
