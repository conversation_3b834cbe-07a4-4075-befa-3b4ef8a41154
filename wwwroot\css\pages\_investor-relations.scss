.investor-relations {
    .inner-content {
        padding-bottom: 0;
    }
}
.ir-layout {
    .hp-section {
        padding: 9rem 0;
    }
    .hp-title {
        color: #000;
        font-size: 4rem;
        font-style: italic;
        font-weight: 500;
        line-height: 1;

        @media screen and (max-width: 1199px) {
            font-size: 3.6rem;
        }
        @media screen and (max-width: 767px) {
            font-size: 2.8rem;
        }
    }

    .hp-view-more {
        color: #f49221;
        font-size: 1.8rem;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon {
            width: 2.2rem;
            height: 2.2rem;
            border-radius: 50%;
            margin-left: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;

            transition: all 0.3s ease;
        }
        &:hover {
            .icon {
                transform: rotate(45deg);
            }
        }
    }

    .ac-section {
        background: #f9f9f9;
        .ac__title {
            margin-bottom: 3.3rem;
            text-align: center;
        }
    }

    .is-section {
        .is__sub-title {
            text-align: center;
            margin-bottom: 3rem;
            color: #1b73c0;
            font-size: 2.4rem;
            font-style: italic;
            font-weight: 600;
            line-height: 1;
            @media screen and (max-width: 767px) {
                font-size: 2rem;
            }
        }

        .is__title {
            text-align: center;
            margin-bottom: 7.5rem;
        }

        .is__list {
            display: flex;
            align-items: stretch;
            justify-content: center;
            flex-wrap: wrap;
            margin-left: -6rem;
            margin-bottom: -6rem;
            .is__item {
                margin-left: 6rem;
                margin-bottom: 6rem;
                width: calc((100% / 3) - 6rem);
                transition: all 0.3s ease;
                border-radius: 10px;
                border: 1px solid #e3e3e3;
                padding: 6rem 4rem;
                background: #fbfbfb;
                display: flex;
                flex-direction: column;
                gap: 3rem;
                .is__item-icon {
                    display: flex;
                    width: 8rem;
                    height: 8rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    border: 1px solid #1b73c0;
                    background: rgba(27, 115, 192, 0.06);
                    backdrop-filter: blur(3px);
                    transition: all 0.3s linear;
                    position: relative;
                    img {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        transition: all 0.3s linear;
                    }
                    .icon--default {
                        opacity: 1;
                        visibility: visible;
                    }
                    .icon--active {
                        opacity: 0;
                        visibility: hidden;
                    }
                }
                .is__item-title {
                    color: #333;
                    font-size: 2.6rem;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                }

                .is__item-link {
                    text-align: left;
                    justify-content: flex-start;
                    margin-top: auto;
                }

                &:hover {
                    .is__item-icon {
                        background: #1b73c0;
                        .icon--default {
                            opacity: 0;
                            visibility: hidden;
                        }
                        .icon--active {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                }
            }
            @media screen and (max-width: 1199px) {
                margin-left: -2rem;
                .is__item {
                    padding: 3rem 2rem;
                    width: calc((100% / 3) - 2rem);
                    margin-left: 2rem;
                    .is__item-title {
                        font-size: 2.4rem;
                    }
                }
            }
            @media screen and (max-width: 767px) {
                .is__item {
                    width: 100%;
                    padding: 3rem 2rem;
                }
            }
        }
    }

    .cgph-section {
        // padding-bottom: 0;
        background: #f9f9f9;

        .cgph-wrapper {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            gap: 6rem;
            .cg-section {
                width: calc(50% - 3rem);
                .cg__title {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 3rem;
                    color: #1b73c0;
                    font-size: 2.4rem;
                    font-weight: 600;
                    margin-bottom: 4rem;
                    @media screen and (max-width: 1199px) {
                        font-size: 1.6rem;
                    }
                }
                .cg__content {
                    color: #333;
                    font-size: 3rem;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 1.556;
                    margin-bottom: 3.6rem;
                    @media screen and (max-width: 767px) {
                        font-size: 2.4rem;
                    }
                }
                .cg__list {
                    display: flex;
                    flex-direction: column;
                    gap: 3rem;
                    .cg__item {
                        .box-href {
                            display: flex;
                            align-items: center;
                            gap: 2rem;
                            .cg__item-icon {
                                width: 4.7rem;
                                height: 4.7rem;
                                border-radius: 50%;
                                border: 1px solid #1b73c0;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }
                            .cg__item-title {
                                color: #333;
                                font-size: 1.8rem;
                                font-style: normal;
                                font-weight: 400;
                                line-height: normal;
                            }
                        }
                        
                    }
                }
            }
            .ph-section {
                width: calc(50% - 3rem);

                .ph-sample {
                    font-size: 0;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }
            }
            @media screen and (max-width: 991px) {
                flex-direction: column;
                gap: 3rem;
                .cg-section {
                    width: 100%;
                }
                .ph-section {
                    width: 100%;
                }
            }
        }
    }
}
