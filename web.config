﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\umb-insilico-medicine-ir.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
  <system.webServer>
	<httpProtocol>
		<customHeaders>
			<remove name="X-Powered-By" />
			<add name="X-Frame-Options" value="SAMEORIGIN" />			
			<add name="X-Permitted-Cross-Domain-Policies" value="none" />
			<add name="X-Content-Type-Options" value="nosniff" />
			<add name="Content-Security-Policy"
					value="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.eurolandir.com *.gstatic.cn *.gstatic.com *.recaptcha.net *.euroland.com *.google-analytics.com *.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; frame-src 'self' *.recaptcha.net *.euroland.com *.eurolandir.com https://www.google.com https://marketplace.umbraco.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: *.euroland.com *.eurolandir.com https://www.gravatar.com https://umbraco.tv *.umbraco.com *.umbraco.org *.googletagmanager.com *.google-analytics.com *.bing.com *.google.com *.github.com github.com; connect-src *.recaptcha.net opendart.fss.or.kr 'self' data: *.bing.com *.eurolandir.com *.euroland.com *.google-analytics.com *.umbraco.org *.umbraco.com; media-src 'self' https://player.vimeo.com; worker-src blob:;frame-ancestors 'self' *.recaptcha.net; form-action 'self'" />
				<!--Strict Transport Security Header-->
			<add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
		</customHeaders>
	</httpProtocol>
	<security>
		<requestFiltering removeServerHeader="true">
			<requestLimits maxAllowedContentLength="100000001" />
		</requestFiltering>
	</security>
</system.webServer>
</configuration>
<!--ProjectGuid: B0D475AE-B343-41C6-8A5B-9BCB599A47BC-->