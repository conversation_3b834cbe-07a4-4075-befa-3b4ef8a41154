.menu-header {
  height: 100%;
  padding: 0;
  margin-left: auto;
  margin-right: 11rem;

  > .ul-menu {
    display: flex;
    align-items: center;
    height: 100%;

    > li {
      position: relative;
      display: flex;
      align-items: center;
      height: 100%;
      margin: 0 5rem;

      > a {
        color: #000;
        font-size: 1.8rem;
        font-weight: 400;
        line-height: 1.3;
        @include transition();
      }

      > .open-sub {
        position: absolute;
        display: block;
        top: 50%;
        right: 0;
        border-bottom: 1px solid #000;
        border-right: 1px solid #000;
        width: 5px;
        height: 5px;
        transform: rotate(45deg) translateY(-50%);

        @media (min-width: 1279px) {
          display: none !important;
        }
      }

      &.active {
        > a {
          &::after {
            width: 100%;
          }
        }
      }

      &:hover,
      &.active {
        > a {
          color: $color-primary;
        }
      }

      &.hassub {
        padding-right: 1.5rem;
      }

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }

      @media (max-width: 1700px) {
        html[lang="cn"] &,
        html[lang="hk"] & {
          margin: 0 3rem;
        }
      }

      @media (max-width: 1500px) {
        margin: 0 3rem;
      }

      @media (max-width: 1400px) {
        html[lang="cn"] &,
        html[lang="hk"] & {
          margin: 0 2rem;
        }
      }
    }
  }

  .menu-pc {
    > li > ul {
      max-width: 23rem;
    }

    > li li .open-sub {
      display: none;
    }

    li.level-4 li {
      display: none;
    }

    li.back {
      display: none;
    }
  }

  .menu-item.level-1,
  .menu-item.level-2 {
    position: relative;
    cursor: pointer;

    &:after {
      content: "";
      height: 2px;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -1px;
      width: 0;
      background: #3fbabc;
      transition: all 0.5s ease;
    }

    > .wrap-sub-menu {
      display: none;
      position: absolute;
      top: 100%;
      left: 50%;
      width: auto;
      height: auto;
      text-align: center;
      background-color: #fff;
      transform: translateX(-50%);
      border-radius: 0 0 4.2px 4.2px;
      padding-top: 0px;
      box-shadow: 0 5px 8px rgba(0, 0, 0, 0.1);
      padding-bottom: 1.5rem;
      z-index: 1;

      > .ul-menu > li {
        position: relative;
        display: block;
        padding: 1.2rem 5.6rem 1.2rem 2.5rem;
        font-size: 1.5rem;
        text-align: left;
        letter-spacing: 1px;
        position: relative;
        transition: all 0.3s ease-in-out;
        white-space: nowrap;
        @include transition();

        a {
          position: relative;
          display: block;
          // font-size: 1.8rem;
          font-size: 15px;
          font-weight: 400;
          color: #515b5d;
          line-height: 1.5;
          z-index: 1;
          @include transition();
        }

        i {
          background: url("/media/ytthik5a/menu_ar.png") no-repeat center center;
          height: 16px;
          width: 25px;
          position: absolute;
          top: 50%;
          margin-top: -8px;
          right: 20px;
          transition: all 0.8s ease-in-out;
          transform: translateX(-45px);
          opacity: 0;
          background-size: 18px auto;
          z-index: 1;
        }

        &::before {
          content: "";
          height: 1px;
          background: #e6e6e6;
          position: absolute;
          left: 25px;
          right: 25px;
          top: 0px;
        }

        &::after {
          content: "";
          background: #1b73c0;
          position: absolute;
          left: 0;
          top: 0;
          width: 0;
          bottom: 0;
          transition: all 0.3s ease-in-out;
        }

        &:first-child {
          &::before {
            display: none;
          }
        }

        &:hover,
        &:focus {
          a {
            color: #fff !important;
          }

          &::after {
            width: 100%;
          }
          i {
            transform: translateX(0);
            opacity: 1;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      li.level-3 {
        position: relative;

        .ul-menu {
          position: absolute;
          top: 0;
          left: 100%;
          width: 200px;
          background-color: $color-primary;
          transform: translateY(10px);
          @include transition();
          opacity: 0;
          visibility: hidden;
          > li {
            border-bottom: 1px solid rgba(255, 255, 255, 0.26);
            @include transition();
          }
        }

        &:hover {
          .ul-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
          }
        }
      }
    }

    > .open-sub {
      display: inline-block;
      @include transition();
    }

    &:hover,
    &.active {
      &::after {
        width: 45px;
      }

      > .open-sub {
        top: 4.3rem;
        transform: rotate(225deg);
      }
    }
  }

  .multi-level.level-3 {
    .ul-menu {
      display: none;
    }
  }

  @media (max-width: 1700px) {
    html[lang="cn"] &,
    html[lang="hk"] & {
      margin-right: 3rem;
    }
  }
  @media (max-width: 1500px) {
    margin-right: 3rem;
  }

  @media (max-width: 1279px) {
    display: none;
  }
}

.header .menu-mobile {
  position: fixed;
  height: 100%;
  left: 25%;
  top: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 920;
  overflow-y: auto;
  color: #fff;
  font-size: 14px;
  box-shadow: -2px 0px 4px 3px rgba(0, 0, 0, 0.06);
  padding: 35px 20px;
  transition: all 0.5s ease-in-out;
  opacity: 0;
  transform: translateX(100%);
  transition-delay: 0.2s;
  display: block;
  background: #1b73c0f2;
  background-size: cover;

  > ul.menu-responsive {
    overflow-y: scroll;
  }

  .menu-responsive {
    display: none;
    padding: 0;
    width: 100%;
    list-style: none;
    background-color: #fff;
    position: relative;
    z-index: 10;

    .menu-item.level-1,
    .menu-item.level-2 {
      > a {
        text-transform: uppercase !important;
      }
    }

    ul {
      float: none;
      height: auto;
    }

    .back-container {
      display: none;
    }

    .ul-menu {
      list-style: none;
      display: none;

      li span {
        padding-left: 2.5rem;
        font-size: 14px;
      }

      .menu-item.level-2,
      .menu-item.level-3 {
        .open-sub {
          display: none;
        }
      }
    }

    > .menu-item {
      position: relative;

      &:not(.hassub) {
        .open-sub {
          display: none;
        }
      }

      &.hassub {
        .open-sub {
          position: absolute;
          right: 0;
          top: 2.5rem;
          transform: translateY(-50%);
          height: 47px;
          width: 47px;

          .mToggle {
            display: inline-block;
            height: 47px;
            width: 47px;
            background: url("/media/lsuhchkw/marr.svg") no-repeat center center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-size: 20px auto;
          }
        }

        &.open {
          > .open-sub:after {
            transform: translateX(-50%) rotate(90deg);
          }

          .mToggle {
            transform: rotate(180deg);
          }
        }
      }
    }

    .toggle-sub {
      display: none;
    }

    a {
      display: block;
      width: 100%;
      font-size: 15px;
      line-height: 5rem;
      text-align: left;
      color: #fff;
      position: relative;
      border-bottom: 1px solid rgba(255, 255, 255, 0.4);
    }

    svg {
      display: none;
    }

    @media (max-width: 1200px) {
      display: block;
      background-color: transparent;
      padding: 1rem 0;
    }

    // @media (max-width:600px) {
    //   margin: 0 -1.5rem;
    //   width: calc(100% + 3rem);
    // }
  }

  body.open-menu & {
    opacity: 1;
    transform: translateX(0%);
    transition-delay: 0.4s;
  }
}

.header.menu-mobile-off-canvas .menu-mobile {
  padding: 0 20px;
  width: 100%;
  list-style: none;
  background-color: #2370d5;
  position: fixed;
  height: calc(100% - 7.5rem);
  left: 0;
  top: 7.5rem;
  padding-top: 1rem;
  transform: translateX(103%);
  transition: all 0.5s ease-in-out;
  z-index: 10;

  &.active {
    transform: translateX(0);
  }
}

.header.menu-mobile-off-canvas .menu-mobile .menu-responsive {
  ul {
    float: none;
  }

  .back {
    position: relative;
    background: #367ad4;
    margin-top: 20px;

    a {
      color: #333 !important;
      font-size: 14px;
    }

    svg {
      position: absolute;
      top: 50%;
      left: 2rem;
      transform: translate(-50%, -50%);
      transition: all 0.5s ease-in-out;
    }
  }

  .ul-menu {
    list-style: none;
    position: fixed;
    width: 100%;
    top: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #2370d5;
    left: 0;
    transform: translateX(103%);
    transition: all 0.5s ease-in-out;
    z-index: 1;
  }

  > .menu-item {
    position: relative;

    &:not(.hassub) {
      .open-sub {
        display: none;
      }
    }

    &.hassub {
      .open-sub {
        position: absolute;
        top: 0;
        right: 0;
        width: 40px;
        height: 40px;

        i {
          position: absolute;
          color: #333;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(-90deg);
          transition: all 0.5s ease-in-out;
        }
      }

      &.open {
        .ul-menu {
          transform: translateX(12px);
        }
      }
    }
  }

  a {
    display: block;
    width: 100%;
    font-size: 14px;
    padding: 0 1.5rem;
    line-height: 5rem;
    text-align: left;
    color: #333;
    position: relative;
    text-decoration: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    span {
      color: #333;
    }
  }
}

.header body {
  position: relative;

  &.open-menu {
    .background-opacity {
      opacity: 1;
      visibility: visible;
    }
  }

  &.open-offcanvas {
    .off-canvas {
      opacity: 1;
      visibility: visible;
      transform: translateX(0);
    }
  }
}
