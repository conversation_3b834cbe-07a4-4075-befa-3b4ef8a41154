@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.Models.Blocks;
@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage
@{
	Layout = "childPageLayout.cshtml";

	var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
	var languageCodeTool = "en-GB";

	if (language.Equals("en-US"))
	{
		language = "en";
		languageCodeTool = "en-GB";
	}
	else if (language.Equals("zh-CN"))
	{
		language = "cn";
		languageCodeTool = "cn";
	}
	else if (language.Equals("zh-HK"))
	{
		language = "hk";
		languageCodeTool = "hk";
	}

	var home = Model.Root();
	var financialReportsPage = home.DescendantsOfType("financialReportsPage")?.FirstOrDefault();
	var listQuickLink = financialReportsPage.Value<BlockListModel>("listQuickLink")?.Select(block => block.Content).ToList() ?? new
	List<IPublishedElement>();

	var contents = financialReportsPage?.Children().Where(x => x.IsVisible())
	.ToList() ?? new List<IPublishedContent>();

	// Pagination logic
	int itemsPerPage = 8;
	int currentPage = 1;
	if (int.TryParse(Context.Request.Query["page"], out int page) && page > 0)
	{
		currentPage = page;
	}

	int totalItems = contents.Count;
	int totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);
	int skip = (currentPage - 1) * itemsPerPage;
	var pagedContents = contents.Skip(skip).Take(itemsPerPage).ToList();

	var titleFr = financialReportsPage.Value<string>("pageTitle");
	var dataTool = Model.Value<string>("dataTool");
	var version = Model.Value<string>("version");
}

<div class="financial-reports-layout">
	<div class="container">
		<div class="fr-header header-common">
			<h1 class="title">@titleFr</h1>
			<div class="rule"></div>
		</div>

		<div class="quicklinks">
			@foreach(var item in listQuickLink)
			{
				var title = item.Value("title");
				var link = item.Value<IPublishedContent>("linkUrl")?.Url() ?? "";
				var icon = item.Value<IPublishedContent>("photo")?.Url()+ "?width=50" ?? "";
					
				<div class="item">
					<div class="box-icon">
						<img src="@icon" alt="@title">
					</div>
					
					<a href="@link" target="_blank">@title</a>
				</div>
			}
		</div>

		<div class="box-tool">
			@if (!string.IsNullOrEmpty(dataTool))
			{
				<euroland-tool data-tool-name="@dataTool"
							data-tool-version="@version"
							data-tool-language="@language">
				</euroland-tool>
			}
			else
			{
				<div class="box-img">
					<img src="/media/migfoc5g/f01.png" alt="tool-img">
				</div>
			}
		</div>

		<div class="list-rp">
			@if (pagedContents.Any())
			{
				int index = skip; // Start index from the current page offset
				foreach (var item in pagedContents)
				{
					var title = item.Value("title");
					var publishedDate = item.Value<DateTime>("publishedDate");
					var file = item.Value<IPublishedContent>("file")?.Url() ?? "";
					var photoimg = item.Value<IPublishedContent>("photo")?.Url() + "?width=296" ?? "";

					<div class="item" data-index="@index">
						<div class="box-img">
							<img src="@photoimg" alt="@title" />
						</div>

						<div class="box-info">
							<div class="left">
								<div class="date">@publishedDate.ToString("yyyy")</div>
								<div class="title">@title</div>
							</div>

							<div class="right">
								<a class="pdf-btn" href="@file" title="@title">
									<svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
										<path d="M22.7725 12.0527V8.59808C22.7725 8.18305 22.6089 7.7851 22.3159 7.49213L16.1978 1.3764C15.9048 1.08344 15.5093 0.919861 15.0942 0.919861H3.63184C2.98486 0.919861 2.45996 1.44476 2.45996 2.09174V12.0527C2.45996 12.1601 2.37207 12.248 2.26465 12.248H0.506836C0.291992 12.248 0.116211 12.4238 0.116211 12.6386V22.4042C0.116211 22.6191 0.291992 22.7949 0.506836 22.7949H2.45996C2.45996 24.5209 3.85889 25.9199 5.58496 25.9199H21.6006C22.2476 25.9199 22.7725 25.395 22.7725 24.748V23.1855C22.7725 22.9706 22.9482 22.7949 23.1631 22.7949H24.7256C24.9404 22.7949 25.1162 22.6191 25.1162 22.4042V12.6386C25.1162 12.4238 24.9404 12.248 24.7256 12.248H22.9678C22.8604 12.248 22.7725 12.1601 22.7725 12.0527ZM17.3037 4.69183L19.0005 6.38861H17.4014C17.3477 6.38861 17.3037 6.34467 17.3037 6.29096V4.69183ZM4.21777 2.48236H14.1787C15.0405 2.48236 15.7412 3.18304 15.7412 4.04486V7.56049C15.7412 7.77533 15.917 7.95111 16.1318 7.95111H19.6475C20.5093 7.95111 21.21 8.6518 21.21 9.51361V12.0527C21.21 12.1601 21.1221 12.248 21.0147 12.248H4.21777C4.11035 12.248 4.02246 12.1601 4.02246 12.0527V2.67767C4.02246 2.57025 4.11035 2.48236 4.21777 2.48236ZM21.0147 24.3574H5.58496C4.72315 24.3574 4.02246 23.6567 4.02246 22.7949H21.0147C21.1221 22.7949 21.21 22.8828 21.21 22.9902V24.1621C21.21 24.2695 21.1221 24.3574 21.0147 24.3574ZM23.3584 21.2324H1.87402C1.7666 21.2324 1.67871 21.1445 1.67871 21.0371V14.0058C1.67871 13.8984 1.7666 13.8105 1.87402 13.8105H23.3584C23.4658 13.8105 23.5537 13.8984 23.5537 14.0058V21.0371C23.5537 21.1445 23.4658 21.2324 23.3584 21.2324Z" fill="#A3A3A3"/>
										<path d="M7.11377 18.2929V20.0678H6.02734V14.975H7.78271C9.0498 14.975 9.68457 15.5121 9.68457 16.5864C9.68457 17.1088 9.49414 17.5263 9.11084 17.8412C8.72754 18.1562 8.24902 18.3076 7.67529 18.2929H7.11377ZM7.11377 15.8173V17.4628H7.58496C8.22217 17.4628 8.54199 17.1845 8.54199 16.6303C8.54199 16.0883 8.22705 15.8173 7.59473 15.8173H7.11377ZM10.7979 20.0703V14.975H12.5581C14.3672 14.975 15.2705 15.8027 15.2705 17.4579C15.2705 18.2441 15.0166 18.8764 14.5112 19.3549C14.0059 19.831 13.3516 20.0703 12.5508 20.0703H10.7979ZM11.8843 15.8637V19.1816H12.4751C12.9902 19.1816 13.3955 19.0278 13.6885 18.7202C13.9814 18.4125 14.1304 17.9975 14.1304 17.4726C14.1304 16.9672 13.9766 16.5717 13.6689 16.2861C13.3613 16.0029 12.9634 15.8613 12.4727 15.8613H11.8843V15.8637ZM19.2549 15.8637H17.436V17.1723H19.1108V18.061H17.436V20.0703H16.3496V14.975H19.2549V15.8637Z" fill="#A3A3A3"/>
									</svg>

									<svg class="svg-hover" xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 26 26" fill="none">
										<path d="M22.7725 12.0527V8.59811C22.7725 8.18308 22.6089 7.78513 22.3159 7.49216L16.1978 1.37643C15.9048 1.08347 15.5093 0.919891 15.0942 0.919891H3.63184C2.98486 0.919891 2.45996 1.44479 2.45996 2.09177V12.0527C2.45996 12.1601 2.37207 12.248 2.26465 12.248H0.506836C0.291992 12.248 0.116211 12.4238 0.116211 12.6386V22.4043C0.116211 22.6191 0.291992 22.7949 0.506836 22.7949C1.58552 22.7949 2.41857 23.7491 3.05037 24.6234C3.61795 25.4088 4.5417 25.9199 5.58496 25.9199H21.6006C22.2476 25.9199 22.7725 25.395 22.7725 24.748V23.1855C22.7725 22.9707 22.9482 22.7949 23.1631 22.7949H24.7256C24.9404 22.7949 25.1162 22.6191 25.1162 22.4043V12.6386C25.1162 12.4238 24.9404 12.248 24.7256 12.248H22.9678C22.8604 12.248 22.7725 12.1601 22.7725 12.0527ZM17.3037 5.89167C17.3037 5.44891 17.839 5.22718 18.1521 5.54025C18.4652 5.85333 18.2434 6.38864 17.8007 6.38864H17.4014C17.3477 6.38864 17.3037 6.3447 17.3037 6.29099V5.89167ZM4.21777 2.48239H14.1787C15.0405 2.48239 15.7412 3.18308 15.7412 4.04489V7.56052C15.7412 7.77536 15.917 7.95114 16.1318 7.95114H19.6475C20.5093 7.95114 21.21 8.65183 21.21 9.51364V12.0527C21.21 12.1601 21.1221 12.248 21.0147 12.248H4.21777C4.11035 12.248 4.02246 12.1601 4.02246 12.0527V2.6777C4.02246 2.57028 4.11035 2.48239 4.21777 2.48239ZM21.0147 24.3574H5.58496C4.22967 24.3574 5.12114 22.7949 6.47644 22.7949H21.0147C21.1221 22.7949 21.21 22.8828 21.21 22.9902V24.1621C21.21 24.2695 21.1221 24.3574 21.0147 24.3574ZM23.3584 21.2324H1.87402C1.7666 21.2324 1.67871 21.1445 1.67871 21.0371V14.0058C1.67871 13.8984 1.7666 13.8105 1.87402 13.8105H23.3584C23.4658 13.8105 23.5537 13.8984 23.5537 14.0058V21.0371C23.5537 21.1445 23.4658 21.2324 23.3584 21.2324Z" fill="#00A607"/>
										<path d="M7.67529 18.293C7.36517 18.293 7.11377 18.5444 7.11377 18.8545V19.5247C7.11377 19.8247 6.87056 20.0679 6.57056 20.0679C6.27055 20.0679 6.02734 19.8247 6.02734 19.5247V16.7305C6.02734 15.761 6.81325 14.9751 7.78271 14.9751C9.0498 14.9751 9.68457 15.5122 9.68457 16.5864C9.68457 17.1089 9.49414 17.5264 9.11084 17.8413C8.72754 18.1562 8.24902 18.3076 7.67529 18.293ZM7.59473 15.8174C7.3291 15.8174 7.11377 16.0327 7.11377 16.2983V16.9917C7.11377 17.2519 7.32473 17.4629 7.58496 17.4629C8.22217 17.4629 8.54199 17.1846 8.54199 16.6304C8.54199 16.0884 8.22705 15.8174 7.59473 15.8174ZM12.5508 20.0703C11.5827 20.0703 10.7979 19.2855 10.7979 18.3174V16.7354C10.7979 15.7632 11.5859 14.9751 12.5581 14.9751C14.3672 14.9751 15.2705 15.8027 15.2705 17.458C15.2705 18.2441 15.0166 18.8765 14.5112 19.355C14.0059 19.8311 13.3516 20.0703 12.5508 20.0703ZM11.8843 15.8638V18.5908C11.8843 18.9171 12.1488 19.1816 12.4751 19.1816C12.9902 19.1816 13.3955 19.0278 13.6885 18.7202C13.9814 18.4126 14.1304 17.9976 14.1304 17.4727C14.1304 16.9673 13.9766 16.5718 13.6689 16.2861C13.3613 16.0029 12.9634 15.8613 12.4727 15.8613H11.8867C11.8854 15.8613 11.8843 15.8624 11.8843 15.8638ZM19.2549 15.4194C19.2549 15.6648 19.0559 15.8638 18.8105 15.8638H18.0903C17.729 15.8638 17.436 16.1567 17.436 16.5181C17.436 16.8794 17.729 17.1724 18.0903 17.1724H18.6665C18.9119 17.1724 19.1108 17.3713 19.1108 17.6167C19.1108 17.8621 18.9119 18.061 18.6665 18.061H18.2734C17.811 18.061 17.436 18.436 17.436 18.8984V19.5271C17.436 19.8271 17.1928 20.0703 16.8928 20.0703C16.5928 20.0703 16.3496 19.8271 16.3496 19.5271V16.4277C16.3496 15.6255 17 14.9751 17.8022 14.9751H18.8105C19.0559 14.9751 19.2549 15.174 19.2549 15.4194Z" fill="#00A607"/>
									</svg>
								</a>
							</div>
						</div>
					</div>
					index++;
				}
			}
		</div>

		<div class="pagination">
			@if (totalPages > 1)
			{
				<div class="pagination-wrapper">
					@if (currentPage > 1)
					{
						<a href="?page=@(currentPage - 1)" class="pagination-btn prev-btn">
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							Previous
						</a>
					}

					<div class="pagination-numbers">
						@{
							int startPage = Math.Max(1, currentPage - 2);
							int endPage = Math.Min(totalPages, currentPage + 2);

							if (endPage - startPage < 4)
							{
								if (startPage == 1)
								{
									endPage = Math.Min(totalPages, startPage + 4);
								}
								else if (endPage == totalPages)
								{
									startPage = Math.Max(1, endPage - 4);
								}
							}
						}

						@if (startPage > 1)
						{
							<a href="?page=1" class="pagination-number">1</a>
							@if (startPage > 2)
							{
								<span class="pagination-dots">...</span>
							}
						}

						@for (int i = startPage; i <= endPage; i++)
						{
							if (i == currentPage)
							{
								<span class="pagination-number active">@i</span>
							}
							else
							{
								<a href="?page=@i" class="pagination-number">@i</a>
							}
						}

						@if (endPage < totalPages)
						{
							@if (endPage < totalPages - 1)
							{
								<span class="pagination-dots">...</span>
							}
							<a href="?page=@totalPages" class="pagination-number">@totalPages</a>
						}
					</div>

					@if (currentPage < totalPages)
					{
						<a href="?page=@(currentPage + 1)" class="pagination-btn next-btn">
							Next
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none">
								<path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</a>
					}
				</div>

				<div class="pagination-info">
					Showing @(skip + 1) to @(Math.Min(skip + itemsPerPage, totalItems)) of @totalItems results
				</div>
			}
		</div>

	</div>
</div>