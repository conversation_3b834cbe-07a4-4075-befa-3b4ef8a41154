.site-footer {
    background: #024c90;
    color: #fff;
    padding: 7rem 0 0;
    .container-fluid {
        width: 100%;
        height: 100%;
        padding: 0 10.5rem;

        @media (max-width: 1279px) {
            padding: 0 1.5rem;
            width: calc(100% - 3rem) !important;
        }

        .box-ft {
            display: flex;
            flex-wrap: wrap;

            .footer-left {
                width: calc(100%/2 - 6rem);
                margin-right: 6rem;
                @media (max-width: 767px) {
                    width: calc(100%);
                    margin-right: 0;
                }

                @media (min-width: 1267px) and (max-width: 1440px) {
                    width: calc(100%/2);
                    margin-right: 0;
                }
                .footer-cols {
                    .menu-pc {
                        &.ul-menu {
                            display: flex;
                            justify-content: space-between;
                            @media (max-width: 1266px) {
                                display: block;
                            }

                            @media (min-width: 1267px) and (max-width: 1440px) {
                                gap: 1rem;
                            }
                            .menu-item {
                                &.hassub {
                                    position: relative; 
                                    .toggle-sub {
                                        // display: none;
                                        position: absolute;
                                        right: 0;
                                        top: 15px;
                                        width: 12px;
                                        height: 12px;
                                        border: solid #fff;
                                        border-width: 0 2px 2px 0;
                                        display: inline-block;
                                        padding: 2px;
                                        transform: translateY(50%) rotate(45deg);
                                        transition: transform .3s;
                                        @media (min-width: 1267px) {
                                            display: none;
                                        }
                                    }
                                }
                                &.hassub.open {
                                    .wrap-sub-menu {
                                        display: block;
                                    }
                                    .toggle-sub {
                                        transform: translateY(50%) rotate(-135deg);
                                        top: 15px;
                                    }
                                }
                                &.hassub.level-3 {
                                    
                                    .toggle-sub {
                                        display: none;
                                    }
                                }
                                @media (max-width: 1266px) {
                                    border-bottom: .1rem solid rgba(255, 255, 255, 0.15);
                                    padding: 1rem 0;
                                    &.menu-Investor-Relations,
                                    &.menu-About-Us,
                                    &.menu-Our-Services,
                                    &.menu-Our-News {
                                        position: relative;
                                        width: 100%;
                                        
                                        .wrap-sub-menu {
                                            display: none;
                                        }
                                    }
                                }
                                
                                >a {
                                    font-size: 2.2rem;
                                    margin-bottom: 1.5rem;
                                    font-weight: 400;
                                    line-height: 70px;
                                    padding-bottom: 2rem;
                                    margin-bottom: 3rem;
                                    // border-bottom: .1rem solid rgba(255, 255, 255, 0.15);
                                    color: #fff;
                                    @media (max-width: 767px) {
                                        border-bottom: none;
                                        line-height: 2;
                                        font-size: 18px;
                                    }

                                    @media (min-width: 768px) and (max-width: 1799px) {
                                        font-size: 20px;
                                    }


                                }
                                .wrap-sub-menu {
                                    .menu-pc {
                                        &.ul-menu {
                                            display: block;
                                            .menu-item {
                                                @media (max-width: 991px) {
                                                    border-bottom: none;
                                                    padding: 0;
                                                }
                                                >a {
                                                    text-decoration: none;
                                                    font-size: 18px;
                                                    color: #FFFFFF80;
                                                    border-bottom: 0;
                                                    padding-bottom: 0;
                                                    margin-bottom: 0;
                                                    line-height: 3.5;
                                                    font-weight: 500;
                                                    
                                                    @media (max-width: 767px) { 
                                                        line-height: 3;
                                                        font-size: 14px;
                                                    }

                                                    @media (min-width: 768px) and (max-width: 1799px) {
                                                        font-size: 16px;
                                                    }
                                                    &:hover {
                                                        color: #ffffff
                                                    }
                                                }
                                            }
                                        }  
                                    }     
                                }
                            }
                        }
                    }
                }

                .footer-search {
                    margin: 3rem 0;
                    display: flex;
                    align-items: center;
                    border-radius: 3rem;
                    overflow: hidden;
                    width: 32rem;
                    background: #fff;
                    position: relative;
                    @media (max-width: 767px) {
                        width: 100%;
                    }
                    input {
                        line-height: 60px;
                        background: #FFF;
                        height: 6rem;
                        width: 32rem;
                        border-radius: 5em;
                        padding: 0 7rem 0 3.2rem;
                        font-size: 16px;
                        color: #666;
                        border: none;
                    }

                    button {
                        background: #35ADC6 url(/media/jwqa4zvn/foot_ser.png) no-repeat center center;
                        height: 5.2rem;
                        width: 5.2rem;
                        position: absolute;
                        top: 4px;
                        right: 5px;
                        cursor: pointer;
                        border-radius: 50%;
                        transition: all .5s ease;
                        border: none;
                    }
                }
            }

            .footer-right {
                width: calc(100%/2 - 4rem);
                margin-left: 4rem;
                padding-left: 6rem;
                border-left: .1rem solid rgba(255, 255, 255, 0.15);

                @media (max-width: 767px) {
                    width: calc(100%);
                    margin-left: 0;
                    padding-left: 0;
                    border-left: none;
                }

                .footer-logo  {
                    img {
                        max-width: 22rem;
                    }
                    @media (max-width: 767px) {
                        display: none;
                    }
                }

                .footer-info {
                    margin: 2rem 0 3.5rem 0;
                    .item {
                        display: flex;
                        margin-bottom: 2rem;
                        &:hover {
                            .box-left {
                                .box-icon {
                                    background: linear-gradient(100deg,#f59941 0%,#e97d2f 100%);
                                    border: .1rem solid #e97d2f;
                                }
                            }
                        }
                        .box-left {
                            .box-icon {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 4.4rem;
                                height: 4.4rem;
                                border-radius: 50%;
                                border: .1rem solid rgba(255, 255, 255, 0.15);
                                transition: all .9s ease;
                            }
                        }
                        .box-right {
                            .content {
                                p {
                                    font-size: 2rem;
                                    line-height: 36px;
                                    padding-left: 3rem;
                                    position: relative;
                                    cursor: default;
                                    font-weight: 300;
                                    @media (max-width: 767px) { 
                                        font-size: 14px;
                                    }

                                    @media (min-width: 768px) and (max-width: 1799px) { 
                                        font-size: 16px;
                                    }
                                    >strong {
                                        font-weight: 700;
                                        color: #fff;
                                    }
                                    a {
                                        color: #fff;
                                    }
                                }
                            }
                        }
                    }
                }

                .list-qrcode {
                    display: flex;
                    align-items: center;
                    gap: 4rem;
                    margin-bottom: 2rem;
                    @media (max-width: 767px) { 
                        gap: 1rem;
                    }
                    .item {
                        .box-qr {
                            width: 7.8rem;
                            height: 7.8rem;
                            border: .1rem solid rgba(255, 255, 255, 0.15);
                            img {
                                padding: 0.8rem;
                                height: 100%;
                                width: 100%;
                            }
                        }

                        .content {
                            text-align: center;
                            font-size: 2rem;
                            padding-top: 1rem;
                            font-weight: 300;
                            @media (max-width: 767px) { 
                                font-size: 14px;
                            }

                            @media (min-width: 768px) and (max-width: 1799px) { 
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }

        .foot_bom {
            display: flex;
            justify-content: space-between;
            font-size: 1.8rem;
            line-height: 30px;
            padding: 3rem 0;
            border-top: .1rem solid rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.5);
            @media (max-width: 767px) { 
                display: block;
                text-align: center;
                font-size: 12px;
            }

            @media (min-width: 768px) and (max-width: 1799px) { 
                font-size: 16px;
            }


            a {
                color: rgba(255, 255, 255, 0.5);
            }
        }
    }
}