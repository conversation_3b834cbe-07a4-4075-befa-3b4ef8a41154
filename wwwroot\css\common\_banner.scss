// Banner
body {
  .banner .wrap-content {
    .content {
      width: 100%;
      height: 100%;
    }
  }
}

.banner {
  position: relative;
  margin-top: 13.4rem;

  .container {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    .wrap-content {
      display: flex;
      width: 100%;

      h1 {
        font-size: 4rem;
        font-style: italic;
        font-weight: 700;
        line-height: 1.3;
        color: #fff;
        margin-bottom: 3.5rem;
        @media screen and (max-width: 1199px) {
          font-size: 3.6rem;
        }
        @media screen and (max-width: 767px) {
          font-size: 2.8rem;
        }
      }

      .content-description {
        color: #fff;
        font-size: 2rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.4;
        @media screen and (max-width: 767px) {
          font-size: 1.8rem;
        }
      }

      .content {
        width: 80%;

        @media (max-width: 768px) {
          width: 100%;
        }
      }

      .ticker-tool-banner {
        width: 40%;

        @media (max-width: 768px) {
          width: 100%;
        }

        @media (max-width: 423px) {
          transform: translateX(0);
        }
      }

      @media (max-width: 767px) {
        flex-direction: column !important;
        gap: 3.2rem;
        align-items: flex-start;
      }
    }
  }

  .swiper-slide {
    display: flex;
    align-items: center;
    background-position: center;
    background-size: cover !important;
  }

  .banner-bottom {
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 100px;
    display: flex;
    overflow: hidden;
    z-index: 5;

    .ticker-tool-under-banner {
      position: absolute;
      top: -8px;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .file-annual-report {
      position: absolute;
      bottom: 0;
      right: 0;
      height: 100%;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30%;
      clip-path: polygon(60px 0, 100% 0, 100% 100%, 0 100%);

      body.investor-relations &,
      body.home & {
        background-color: #e8e8fe;
      }

      a {
        display: flex;
        align-items: center;
        gap: 1.7rem;

        span {
          color: #30318c;
          text-align: right;
          font-size: 2.4rem;
          font-style: normal;
          font-weight: 600;
          line-height: 1.25;
          text-decoration-line: underline;
          max-width: 234px;

          @media (max-width: 767px) {
            max-width: 158px;
            font-size: 16px;
          }
        }
      }

      @media (max-width: 1320px) {
        width: 40%;
      }

      @media (max-width: 1024px) {
        width: 50%;
      }

      @media (max-width: 767px) {
        width: 75%;
        justify-content: flex-end;
        padding-right: 2rem;
      }
    }
  }

  .banner-fullpage-true {
    .swiper-slide {
      height: calc(100vh - 10rem);
    }
  }

  @media (max-width: 1279px) {
    margin-top: 5.6rem;
  }
}

.tab-button.childpage-banner {
  display: none;
}

.ticker-tool-under-banner {
  display: block;
  transform: translateY(-50px);
  position: relative;
  z-index: 9;
}
