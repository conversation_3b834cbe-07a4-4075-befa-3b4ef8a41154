.full-height-bg {
  height: 100vh;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--section-padding);

  svg {
    width: 815px;
    height: 225px;
    margin-bottom: 50px;

    @media (max-width: 640px) {
      transform: scale(0.8);
    }
  }

  p {
    &.title {
      font-size: 2.5rem;
      font-weight: bold;
      margin-top: -2rem;
      text-align: center;
      color: #fff;
    }

    &.subtitle {
      font-size: 1.25rem;
      margin-top: 0.75rem;
      text-align: center;
      color: white;
    }
  }

  a.back-home {
    font-size: 15px;
    font-weight: bold;
    margin-top: 2rem;
    padding: 1rem 0;
    background-color: var(--primary-100);
    color: #F8F8F8;
    text-transform: uppercase;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 1rem;
      left: 0;
      height: 2px;
      background-color: white;
      width: 0;
      transition: width 0.3s ease-in;
    }

    &:hover::after {
      width: 100%;
    }
  }
}

// For 404 page
.full-height-bg {
  height: 100vh;
  width: 100%;
  background: $color-primary;
  background-size: 100% 300%;
  background-position: top;
  animation: smoothGradient 10s ease-in-out infinite alternate;
}