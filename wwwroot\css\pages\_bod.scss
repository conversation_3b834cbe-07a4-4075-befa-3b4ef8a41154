.bod-section__list {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    .bod {
        padding: 4rem;
        border-radius: 10px;
        background: #FFF;
        @media (max-width: 578px) {
            padding: 2rem;
        }
    }
    .bod__body {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        .bod__info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            .name {
                color:#333;
                font-size: 2.8rem;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 2.3rem;
            }
            .position {
                color:#888;
                font-size: 2rem;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }
        
    }
    .bod__desc {
        color:#888;
        font-size: 1.8rem;
        font-style: normal;
        font-weight: 400;
        line-height: 30px; /* 166.667% */

        padding-top: 2.2rem ;
        margin-top: 2.2rem;
        border-top: 1px solid #E5E5E5;
    }
    .bod__button {
        cursor: pointer;
        min-width: 42px;
        min-height: 42px;
        width: 42px;
        height: 42px;
        border-radius: 21px;
        background: #1B73C0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bod__body.active .bod__button {
        transform: rotate(180deg);
        transition: all 0.3s ease-in-out;
    }
}
.corporate-governance{
    .inner-content{
        background-color: #F9F9F9;
    }

}