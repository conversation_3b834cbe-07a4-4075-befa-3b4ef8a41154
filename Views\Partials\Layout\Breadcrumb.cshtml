﻿@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@using Umbraco.Cms.Core.WebAssets;
@inject IRuntimeMinifier runtimeMinifier;
@{
    var home = Model.Root();
    var selection = Model.AncestorsOrSelf();
    var count = 0;
}
<ul class="breadcrumb-nav-list animate__animated animate__fadeInUp">
    @foreach (var item in selection.Reverse())
    {
        count++;
        var className = item.Name.ToLower().Replace(" ", "-").Replace("'", "-");
        if (0 <= item.Level && item.Level <= 3)
        {
            if(!Model.IsDocumentType("commonTabcontentPage")){
                <li class="@(item == Model ? "active" : "") breadcrumb-nav-item @className">
                    <a href="@(item == Model ? "javascript:void(0)" : item.Url())" title="@item.Value("pageTitle")">                    
                        @if (item.HasValue("pageTitle") && item.Value("pageTitle") != null)
                        {
                            @item.Value("pageTitle").ToString();
                        }
                    </a>
                    <span class="separator">></span>
                </li>
            } else{
                <li class="breadcrumb-nav-item @className">                        
                    <a href="@item.Url()" title="@item.Value("pageTitle")">
                        @if (item.HasValue("pageTitle") && item.Value("pageTitle") != null)
                        {
                            @item.Value("pageTitle").ToString()
                            ;
                        }
                        else if (item.HasValue("productName") && item.Value("productName") != null)
                        {
                            @item.Value("productName").ToString()
                            ;
                        }
                    </a>
                    <span class="separator">></span>
                </li>
            }               
        }
    }

    @if (Model.IsDocumentType("commonTabcontentPage"))
    {
        <li class="breadcrumb-nav-item last-item active">
            <a href="javascript:void(0)" title="@Model.Value("pageTitle")"></a>
        </li>
    }      
</ul>
