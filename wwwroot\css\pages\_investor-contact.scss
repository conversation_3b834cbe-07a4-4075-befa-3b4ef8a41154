.investor-contact-container {
    display: flex;
    @media (max-width: 1024px) {
        gap: 2rem;
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    .contact-info-section {
        position: relative;
        width: 39%;
        @media (max-width: 1024px) {
            max-height: 40rem;
            width: 100%;
        }
        .background-image {
            height: 100%;
            width: 100%;
            border-top-left-radius: 18px;
            border-bottom-left-radius: 18px;
            @media (max-width: 1024px) {
                border-radius: 18px;
            }
        }
        .contact-info-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .contact-title {
                color: #fff;
                font-size: 4rem;
                font-style: italic;
                font-weight: 600;
                line-height: normal;
                @media screen and (max-width: 1199px) {
                    font-size: 3.6rem;
                }
                @media screen and (max-width: 767px) {
                    font-size: 2.8rem;
                }
            }
            .email-contact {
                margin-top: 2rem;
                display: flex;
                align-items: center;
                gap: 1.5rem;
                a {
                    color: #fff;
                    font-size: 2.2rem;
                    font-weight: 500;
                }
            }
        }
    }
    .contact-form-section {
        flex: 1;
        padding: 4rem;
        border-radius: 0 18px 18px 0;
        background: rgba(126, 187, 240, 0.12);
        width: 61%;

        @media (max-width: 1024px) {
            border-radius: 18px;
            width: 100%;
        }
        .form-title {
            color: #1b73c0;
            font-size: 2.4rem;
            font-style: italic;
            font-weight: 600;
            line-height: normal;
        }
        .form-description {
            color: #333;
            font-size: 1.6rem;
            font-style: normal;
            font-weight: 400;
            line-height: 27px;
            padding: 1.8rem 0;
        }
        .form-row {
            display: flex;
            align-items: flex-start;
            gap: 1.2rem;
        }
    }
}

.contact-form {
    display: grid;
    gap: 1.6rem;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    input,
    textarea {
        border: 0;
        width: 100%;
        padding: 1.6rem 1rem;
        font-size: 1.4rem;

        &::placeholder {
            color: #001f5d;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.5;
        }
    }
    .submit-button {
        border-radius: 100px;
        width: 150px;
        height: 50px;
        border: 1px solid #e5e5e5;
        background: linear-gradient(90deg, #f6922d 0%, #eb7b1f 100%);
        padding: 14px 20px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;

        &:hover {
            background: white;
            border: 1px solid #f6922d;
            color: #f6922d;
        }
    }
    .input-group {
        position: relative;

        &::after {
            content: "*";
            color: red;
            position: absolute;
            // left: 8px;
            top: 15px;
            pointer-events: none;
        }
        &:has(input:not(:placeholder-shown)),
        &:has(textarea:not(:placeholder-shown)),
        &:has(select:valid) {
            &::after {
                display: none;
            }
        }
    }
    .input-group--name {
        &::after {
            left: 38px;
        }
        &:lang(en) {
            &::after {
                left: 50px;
            }
        }
    }
    .input-group--phone {
        &::after {
            left: 38px;
        }
        &:lang(en) {
            &::after {
                left: 52px;
            }
        }
    }
    .input-group--email {
        &::after {
            left: 67px;
        }
        &:lang(en) {
            &::after {
                left: 49px;
            }
        }
    }
    .input-group--company {
        &::after {
            left: 38px;
        }
        &:lang(en) {
            &::after {
                left: 74px;
            }
        }
    }

    .form-input {
        border-radius: 4px;
    }
    .nice-select {
        // height: 5.6rem;
        border-radius: 4px;
        padding: 1.6rem 1rem;
        display: flex;
        align-items: center;
        border: 0;
        font-size: 1.4rem;
        border-bottom: 0 !important;
        height: 5.3rem;

        .current {
            padding: 1.6rem 1rem;
            color: #001f5d;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.5;
        }
    }
}

.toast {
    position: fixed;
    bottom: 10%;
    right: 1.25rem;
    max-width: 40rem;
    padding: 1.5rem;
    color: #fff;
    border-radius: 0.5rem;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 10;

    display: none;
    opacity: 0;
    transform: translateY(2.5rem);
    transition:
        transform 0.3s ease,
        opacity 0.3s ease;
    &.show {
        display: block;
        opacity: 1;
        transform: translateY(0);

        .toast__content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toast__message {
            font-size: 1.8rem;
        }

        .toast__close {
            margin-left: auto;
            background: transparent;
            border: 0;
            cursor: pointer;
            color: #fff;
            font-weight: 700;
            &:hover {
                color: #000;
            }
        }
    }
}

.error-message {
    color: red;
    font-size: 1.4rem;
    margin-top: 2px;
}
