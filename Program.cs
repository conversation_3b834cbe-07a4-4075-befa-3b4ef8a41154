using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using SixLabors.ImageSharp.Web.DependencyInjection;
using umb_insilico_medicine_ir.Services;
using Umbraco.Cms;
using Umbraco.Cms.Web.Common.ApplicationBuilder;

namespace umb_insilico_medicine_ir
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add Logging
            builder.Services.AddLogging(logging =>
            {
                //logging.AddConsole();
                //logging.SetMinimumLevel(LogLevel.Information);
                //logging.SetMinimumLevel(LogLevel.Debug);
            });
            // Optionally add WebOptimizer if you're using it for bundling
            builder.Services.AddWebOptimizer(pipeline =>
            {
                pipeline.AddCssBundle("/bundles/inline-css-bundle.css", new[]
                {
                    "/css/lib/*css",
                    "/css/common/main.css",
                });
                pipeline.AddJavaScriptBundle("/bundles/inline-js-bundle", new[]
                {
                    "/scripts/lib/jquery.min.js",
                    "/scripts/lib/bootstrap.min.js",
                    "/scripts/lib/jquery.magnific-popup.min.js",
                    "/scripts/lib/jquery.nice-select.min.js",
                    "/scripts/lib/aos.js",
                    "/scripts/lib/lazysizes.min.js",
                    "/scripts/lib/swiper-bundle.min.js",
                    "/scripts/lib/text-animate.js",
                    "/scripts/common/*.js",
                    "/scripts/pages/*.js",
                });

                //Minification
                pipeline.MinifyCssFiles();
                pipeline.MinifyJsFiles();
            });
            //builder.Services.Configure<GoogleRecaptchaOptions>(builder.Configuration.GetSection("GoogleRecaptcha"));

            // Add your custom StaticFileOptions configuration here
            builder.Services.AddTransient<IConfigureOptions<StaticFileOptions>, ConfigureStaticFileOptions>();
            // Custom Umbraco
            builder.Services.AddUmbraco(builder.Environment, builder.Configuration)
                .AddBackOffice()
                .AddWebsite()
                .AddDeliveryApi()
                .AddComposers()
                //Add the Azure Blob Storage file system
                .AddAzureBlobMediaFileSystem()
                .AddAzureBlobImageSharpCache()
                .Build();

            var app = builder.Build();

            // Start Umbraco 
            await app.BootUmbracoAsync();

            // Setting Middleware
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/InternalServerError.html");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            // Replace UseWebOptimizer with WebOptimizer middleware (if installed)
            app.UseWebOptimizer();
            app.UseImageSharp();
            app.UseStaticFiles();
            app.UseUmbraco()
                .WithMiddleware(u =>
                {
                    u.UseBackOffice();
                    u.UseWebsite();
                })
                .WithEndpoints(u =>
                {
                    u.UseBackOfficeEndpoints();
                    u.UseWebsiteEndpoints();
                });

            await app.RunAsync();
        }
    }
}