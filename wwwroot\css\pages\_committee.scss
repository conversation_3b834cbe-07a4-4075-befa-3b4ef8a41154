
.committee-section {
    .committee__title {
        color: #232d39;
        text-align: center;
        font-size: 3.2rem;
        font-style: normal;
        font-weight: 700;
        line-height: 1.5; /* 48px */
        margin-bottom: 2rem;
    }
    .box-table {
        overflow: auto;
        .committee__table {
            width: 100%;
            border-collapse: collapse;
            thead {
                th {
                    background:#1B73C0;
                    color: #FFF;
                    font-size: 2rem;
                    font-style: normal;
                    font-weight: 500;
                    line-height: 30px; /* 150% */
                    padding: 3.2rem 1.5rem;
                    &:lang(en) {
                        font-size: 1.8rem;
                    }
                    @media (max-width: 600px) {
                        padding: 1rem;
                        font-size: 1.4rem;
                    }
                }
                .person-top{
                    color: transparent;
                    min-width: 220px;
                }
            }
            .person {
                text-align: start;
                white-space: nowrap;
                min-width: 220px;
            }
            tr {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1.2fr;
                // border-bottom: 1px solid rgba(154, 184, 210, 0.33);
                &:nth-of-type(2n + 1) {
                    background: #1B73C008;
                }
            }
            td {
                padding: 1.4rem 3rem;
                color:#333;
                font-size: 2rem;
                font-style: normal;
                font-weight: 400;
                line-height: 1.5; /* 30px */
            }
            th,
            td {
                text-align: center;
                border-left: 1px solid rgba(154, 184, 210, 0.33);
                border-bottom: 1px solid rgba(154, 184, 210, 0.33);
                min-width: 135px;
                &:last-of-type{
                    border-right: 1px solid rgba(154, 184, 210, 0.33);
                }
                @media (max-width: 600px) {
                    padding: 1rem;
                    font-size: 1.4rem;
                    vertical-align: middle;
                }
            }
        }
    }
    .committee-legend {
        margin-top: 2rem;
        display: flex;
        flex-direction: row;
        gap: 6.1rem;
        align-items: center;
        justify-content: flex-end;
        p {
            display: flex;
            flex-direction: row;
            gap: 1rem;
            align-items: center;
        }
    }
}