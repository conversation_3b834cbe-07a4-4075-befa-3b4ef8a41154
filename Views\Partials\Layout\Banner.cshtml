﻿@using Umbraco.Cms.Core.Models.Blocks
@using Umbraco.Cms.Web.Common.PublishedModels;
@inherits Umbraco.Cms.Web.Common.Views.UmbracoViewPage;
@using Umbraco.Cms.Core.Models
@{
  var carousel = getBannerCarousel();
  var home = Model.Root();
  var pageTitle = Model.Value<string>("pageTitle");
  bool IsHomePage = Model.ContentType.Alias.ToLower().Equals("home") ? true : (Model.HasProperty("isIrSite") &&
  Model.HasValue("isIrSite") && Model.Value<bool>("isIrSite") ?
  Model.ContentType.Alias.ToLower().Equals("investorrelationspage") : false);
  bool IsChildPage = !IsHomePage;
  bool hiddenBanner = Model.HasProperty("hiddenBanner") && Model.HasValue("hiddenBanner") ?
  Model.Value<bool>("hiddenBanner") : false;
  string imageBannerDefault = "/media/banner-default.png";
  var textEffect = Model.HasProperty("textEffect") && Model.HasValue("textEffect") ?
  Model.Value<string>("textEffect").ToLower().Replace(" ", "-").Replace("'", "-") : "animate__fadeInUp";

  if (textEffect.Equals("faded-up")) { textEffect = "animate__fadeInUp"; }
  if (textEffect.Equals("faded-down")) { textEffect = "animate__fadeInDown"; }
  if (textEffect.Equals("faded-right-to-left")) { textEffect = "animate__fadeInRight"; }
  if (textEffect.Equals("faded-left-to-right")) { textEffect = "animate__fadeInLeft"; }

  string dragSlider = Model.HasProperty("dragSlider") && Model.HasValue("dragSlider")
  ? Model.Value<string>("dragSlider").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  string effectSlider = Model.HasProperty("effectSlider") && Model.HasValue("effectSlider")
  ? Model.Value<string>("effectSlider").ToLower().Replace(" ", "-").Replace("'", "-") : "none";
  string enableTool = Model.HasProperty("enableTool") && Model.HasValue("enableTool")
  ? Model.Value<string>("enableTool").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  string enableDot = Model.HasProperty("enableDot") && Model.HasValue("enableDot")
  ? Model.Value<string>("enableDot").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  string enableArrow = Model.HasProperty("enableArrow") && Model.HasValue("enableArrow")
  ? Model.Value<string>("enableArrow").ToLower().Replace(" ", "-").Replace("'", "-") : "false";

  string timingEachSlide = Model.HasProperty("timingEachSlide") && Model.HasValue("timingEachSlide")
  ? Model.Value<string>("timingEachSlide").ToLower().Replace(" ", "-").Replace("'", "-") : "5000";
  string bannerHeight = Model.HasProperty("bannerHeight") && Model.HasValue("bannerHeight")
  ? Model.Value<string>("bannerHeight").ToLower().Replace(" ", "-").Replace("'", "-") : "30";
  string bannerFullPage = Model.HasProperty("bannerFullPage") && Model.HasValue("bannerFullPage")
  ? Model.Value<string>("bannerFullPage").ToLower().Replace(" ", "-").Replace("'", "-") : "false";
  var unit = "rem";
  string cssBannerHeight = bannerHeight + unit;

  var menuItems = home.Children().Where(x => x.IsVisible()).ToList();

  var language = System.Threading.Thread.CurrentThread.CurrentCulture.Name;
  language = language.Equals("en-US") ? "en-gb" : (language.Equals("ar-SA") ? "ar-ae" : language);
}
@* Define style setting *@
<style>
  .banner-wrap.banner-fullpage-false .swiper-slide {
    min-height:
      @cssBannerHeight
    ;
    max-height:
      @cssBannerHeight
    ;
  }
</style>

@functions {
  public IEnumerable<IPublishedElement> getBannerCarousel()
  {
    var carousel = new List<IPublishedElement>();
    int level = Model.Level;
    IPublishedContent currentPage;
    while (level > 0)
    {
      currentPage = Model.AncestorOrSelf(level);

      if (currentPage.HasProperty("bannerCarousel") &&
      currentPage.HasValue("bannerCarousel") &&
      currentPage.Value("bannerCarousel") != null)
      {
        carousel = currentPage.Value<BlockListModel>("bannerCarousel")?.Select(block => block.Content).ToList() ?? new
        List<IPublishedElement>();

        break;
      }
      else
      {
        carousel = currentPage.Parent.Value<BlockListModel>("bannerCarousel")?.Select(block => block.Content).ToList() ?? new
        List<IPublishedElement>();
        break;
      }
      level--;
    }

    return carousel;
  }
}

@if (hiddenBanner)
{
  return;
}

<!-- banner -->
<div
  class="swiper mySwiper banner-wrap banner-swiper dot-@enableDot arrow-@enableArrow banner-fullpage-@bannerFullPage">
  <div class="swiper-wrapper">
    @if (carousel.Any())
    {
      foreach (var item in carousel)
      {
        var photoUrl = (item.HasProperty("image") && item.HasValue("image")) ? item.Value<IPublishedContent>("image")?.Url()
        : imageBannerDefault;
        photoUrl = !string.IsNullOrEmpty(photoUrl) ? $"{photoUrl}?width=1920&format=webp&quality=90" : "";
        var title = item.HasProperty("title") && item.HasValue("title") ? item.Value<string>("title") : "";
        var secondLineTitle = item.HasProperty("secondLineTitle") && item.HasValue("secondLineTitle") ?
        item.Value<string>("secondLineTitle") : "";
        //pageTitle= title -> pageTitle
        pageTitle = title.Length > 0 ? title : pageTitle;
        var hiddenCaption = item.HasProperty("hiddenCaption") && item.HasValue("hiddenCaption") ?
        item.Value<bool>("hiddenCaption") : true;
        var useParentTitle = item.HasProperty("useParentTitle") && item.HasValue("useParentTitle") ?
        item.Value<bool>("useParentTitle") : false;

        if (useParentTitle)
        {
          pageTitle = Model.Parent.Value<string>("pageTitle");
        }

        var quickLink = item.Value<Link>("linkUrl");
        var quickLinkUrl = item.Value<Link>("linkUrl")?.Url ?? "";
        var quickLinkName = item.Value<Link>("linkUrl")?.Name ?? "";

        var quickLinkUrl2 = item.Value<Link>("linkUrl2")?.Url ?? "";
        var quickLinkName2 = item.Value<Link>("linkUrl2")?.Name ?? "";

        var dataToolName = item.HasProperty("dataToolName") && item.HasValue("dataToolName") ?
        item.Value<string>("dataToolName") : "";

        var dataToolVersion = item.HasProperty("dataToolVersion") && item.HasValue("dataToolVersion") ?
        item.Value<string>("dataToolVersion") : "";

        string content = item.HasProperty("content") && item.HasValue("content") ? item.Value<String>("content") : "";
        var videoUrl = item.HasProperty("video") && item.HasValue("video") ? item.Value<IPublishedContent>("video")?.Url()
        ?? "" : "";
        if (hiddenCaption || IsChildPage)
        {
          //disable page title
          quickLinkUrl = "";
        }
        generateBannerItem(
        photoUrl,
        pageTitle,
        quickLink,
        quickLinkUrl,
        quickLinkName,
        quickLinkUrl2,
        quickLinkName2,
        content,
        videoUrl,
        textEffect,
        dragSlider,
        effectSlider,
        timingEachSlide,
        language,
        enableTool,
        dataToolName,
        dataToolVersion)
        ;
      }
    }
    else
    {
      <div class="swiper-slide"
        style="background-image: url('@imageBannerDefault'); background-repeat: no-repeat; background-position: center;"
        data-drag-slider="@dragSlider" data-effect-slider="@effectSlider" data-timing="@timingEachSlide">
        <div class="container h-full">
          <div class="wrap-content">
            <div class="content animate__animated @textEffect">
              @{
                <h1 class="banner-title-homepage first-line">@pageTitle</h1>
              }
            </div>
          </div>
        </div>
      </div>
    }
  </div>
</div>

@functions {
  private void generateBannerItem(
  string photoUrl,
  string pageTitle,
  Link quickLink,
  string quickLinkUrl,
  string quickLinkName,
  string quickLinkUrl2,
  string quickLinkName2,
  string content,
  string videoUrl,
  string textEffect,
  string dragSlider,
  string effectSlider,
  string timingEachSlide,
  string language,
  string enableTool,
  string dataToolName,
  string dataToolVersion)
  {

    if (!string.IsNullOrEmpty(@videoUrl))
    {
      <div class="swiper-slide background-video" data-drag-slider="@dragSlider" data-effect-slider="@effectSlider"
        data-timing="@timingEachSlide">
        <video autoplay muted loop id="myVideo">
          <source src="@videoUrl" type="video/mp4">
          <track kind="captions" srclang="en" label="captions">
        </video>
        <div class="container h-full">
          <div class="wrap-content ">
            <div class="content animate__animated @textEffect">
              @{
                <h1 class="banner-title-homepage first-line">@pageTitle</h1>
                <div class="content-description">@Html.Raw(content)</div>
                @if (!string.IsNullOrEmpty(quickLinkUrl) || !string.IsNullOrEmpty(quickLinkUrl2))
                {
                  <div class="wrap-button-banner">
                    @if (!string.IsNullOrEmpty(quickLinkUrl))
                    {
                      <a class="medium button btn-banner btn-alt" title="@quickLinkName" href="@quickLinkUrl"
                        target="_self">@quickLinkName</a>
                    }
                    @if (!string.IsNullOrEmpty(quickLinkUrl2))
                    {
                      <a class="medium button btn-banner" title="@quickLinkName2" href="@quickLinkUrl2"
                        target="_self">@quickLinkName2</a>
                    }
                  </div>
                }
              }
            </div>
            @if (enableTool == "true" && dataToolName != "")
            {
              <div class="ticker-tool-banner @dataToolName @dataToolVersion">
                <euroland-tool data-tool-name="@dataToolName" data-tool-version="@dataToolVersion"
                  data-tool-language="@language">
                </euroland-tool>
              </div>
            }
            @{
            }
          </div>
        </div>
      </div>
    }
    else
    {
      <div class="swiper-slide bg-center bg-no-repeat bg-cover @(Model.ContentType.Alias.ToLower() == "homepage" ? "" : "")"
        style="background-image: url('@photoUrl');" data-drag-slider="@dragSlider" data-photo-url="@photoUrl"
        data-effect-slider="@effectSlider" data-timing="@timingEachSlide">
        <div
          class="lock-screen text-secondarylight-100 relative container @(Model.ContentType.Alias.ToLower() == "homepage" ? "" : "")">
          <div class="wrap-content">
            <div class="content animate__animated @textEffect">
              @{
                <h1 class="first-line">@pageTitle</h1>
                <div class="content-description">@Html.Raw(content)</div>
                @if (!string.IsNullOrEmpty(quickLinkUrl) || !string.IsNullOrEmpty(quickLinkUrl2))
                {
                  <div class="wrap-button-banner">
                    @if (!string.IsNullOrEmpty(quickLinkUrl))
                    {
                      <a class="medium button btn-banner btn-alt" title="@quickLinkName" href="@quickLinkUrl"
                        target="_self">@quickLinkName</a>
                    }
                    @if (!string.IsNullOrEmpty(quickLinkUrl2))
                    {
                      <a class="medium button btn-banner" title="@quickLinkName2" href="@quickLinkUrl2"
                        target="_self">@quickLinkName2</a>
                    }
                  </div>
                }
              }
            </div>
            @if (enableTool == "true" && dataToolName != "")
            {
              <div class="ticker-tool-banner @dataToolName @dataToolVersion">
                <euroland-tool data-tool-name="@dataToolName" data-tool-version="@dataToolVersion"
                  data-tool-language="@language">
                </euroland-tool>
              </div>
            }
          </div>
        </div>
      </div>
    }
  }
}
